using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using HWSAuditPlatform.Infrastructure.Services.ActiveDirectory;
using HWSAuditPlatform.Infrastructure.Services.FileStorage;

namespace HWSAuditPlatform.Infrastructure.HealthChecks;

/// <summary>
/// Health check for Active Directory connectivity
/// </summary>
public class ActiveDirectoryHealthCheck : IHealthCheck
{
    private readonly IActiveDirectoryService _activeDirectoryService;
    private readonly ILogger<ActiveDirectoryHealthCheck> _logger;

    public ActiveDirectoryHealthCheck(
        IActiveDirectoryService activeDirectoryService,
        ILogger<ActiveDirectoryHealthCheck> logger)
    {
        _activeDirectoryService = activeDirectoryService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var isConnected = await _activeDirectoryService.TestConnectionAsync(cancellationToken);

            if (isConnected)
            {
                return HealthCheckResult.Healthy("Active Directory connection is working");
            }
            else
            {
                return HealthCheckResult.Unhealthy("Unable to connect to Active Directory");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Active Directory health check failed");
            return HealthCheckResult.Unhealthy("Active Directory health check failed", ex);
        }
    }
}

/// <summary>
/// Health check for file storage service
/// </summary>
public class FileStorageHealthCheck : IHealthCheck
{
    private readonly IFileStorageService _fileStorageService;
    private readonly ILogger<FileStorageHealthCheck> _logger;

    public FileStorageHealthCheck(
        IFileStorageService fileStorageService,
        ILogger<FileStorageHealthCheck> logger)
    {
        _fileStorageService = fileStorageService;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Test file storage by checking if a test file exists
            // This is a simple connectivity test
            var testFileName = "health-check-test.txt";
            var exists = await _fileStorageService.FileExistsAsync(testFileName, cancellationToken);

            // The test passes regardless of whether the file exists
            // We're just testing that the storage service is accessible
            return HealthCheckResult.Healthy("File storage service is accessible");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "File storage health check failed");
            return HealthCheckResult.Unhealthy("File storage service is not accessible", ex);
        }
    }
}
