using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Domain.Entities.Organization;

/// <summary>
/// Represents an area within a factory (e.g., Production Line A, Warehouse).
/// Maps to the Areas table in the database.
/// </summary>
public class Area : AuditableEntity<int>
{
    /// <summary>
    /// Name of the area
    /// </summary>
    [Required]
    [MaxLength(150)]
    public string AreaName { get; set; } = string.Empty;

    /// <summary>
    /// Links to the parent factory
    /// </summary>
    public int FactoryId { get; set; }

    /// <summary>
    /// Navigation property for the parent factory
    /// </summary>
    public virtual Factory Factory { get; set; } = null!;

    /// <summary>
    /// Optional description of the area
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Indicates if the area is currently active/relevant
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Navigation property for sub-areas within this area
    /// </summary>
    public virtual ICollection<SubArea> SubAreas { get; set; } = new List<SubArea>();

    /// <summary>
    /// Navigation property for the user who created this area
    /// </summary>
    public virtual User? CreatedByUser { get; set; }

    /// <summary>
    /// Navigation property for the user who last updated this area
    /// </summary>
    public virtual User? UpdatedByUser { get; set; }
}
