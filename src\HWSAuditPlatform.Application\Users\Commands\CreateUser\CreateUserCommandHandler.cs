using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Users;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Commands.CreateUser;

/// <summary>
/// Handler for CreateUserCommand
/// </summary>
public class CreateUserCommandHandler : BaseCommandHandler<CreateUserCommand, string>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateUserCommandHandler(IApplicationDbContext context, ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<string> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // Get the role entity
        var role = await _context.Roles
            .FirstOrDefaultAsync(r => r.RoleName == request.Role, cancellationToken);

        if (role == null)
        {
            throw new InvalidOperationException($"Role '{request.Role}' not found");
        }

        // Create the user entity
        var user = User.Create(
            username: request.Username,
            firstName: request.FirstName,
            lastName: request.LastName,
            email: request.Email,
            roleId: role.Id,
            factoryId: request.FactoryId,
            isActive: request.IsActive,
            adObjectGuid: request.AdObjectGuid,
            adDistinguishedName: request.AdDistinguishedName,
            createdByUserId: _currentUserService.UserId);

        // Add to context
        await _context.Users.AddAsync(user, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return user.Id;
    }
}
