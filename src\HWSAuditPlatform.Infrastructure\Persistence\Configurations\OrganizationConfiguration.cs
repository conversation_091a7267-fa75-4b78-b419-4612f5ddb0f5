using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Organization;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for Location entity
/// </summary>
public class LocationConfiguration : IEntityTypeConfiguration<Location>
{
    public void Configure(EntityTypeBuilder<Location> builder)
    {
        builder.ToTable("Location");

        // Primary Key
        builder.HasKey(l => l.Id);
        builder.Property(l => l.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(l => l.LocationName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(l => l.LocationCountry)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(l => l.LocationCountryCode)
            .HasMaxLength(10)
            .IsRequired(false);

        builder.Property(l => l.OwnerGroupId)
            .HasMaxLength(100)
            .IsRequired();

        // Base entity properties
        builder.Property(l => l.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(l => l.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        // Indexes
        builder.HasIndex(l => l.LocationName)
            .IsUnique()
            .HasDatabaseName("IX_Location_LocationName");

        builder.HasIndex(l => l.LocationCountryCode)
            .IsUnique()
            .HasDatabaseName("IX_Location_LocationCountryCode")
            .HasFilter("[LocationCountryCode] IS NOT NULL");

        // Ignore domain events
        builder.Ignore(l => l.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for Factory entity
/// </summary>
public class FactoryConfiguration : IEntityTypeConfiguration<Factory>
{
    public void Configure(EntityTypeBuilder<Factory> builder)
    {
        builder.ToTable("Factories");

        // Primary Key
        builder.HasKey(f => f.Id);
        builder.Property(f => f.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(f => f.FactoryName)
            .HasMaxLength(150)
            .IsRequired();

        builder.Property(f => f.FactoryProcess)
            .HasMaxLength(2000)
            .IsRequired(false);

        builder.Property(f => f.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Address value object
        builder.OwnsOne(f => f.Address, address =>
        {
            address.Property(a => a.AddressLine1)
                .HasColumnName("AddressLine1")
                .HasMaxLength(255);

            address.Property(a => a.City)
                .HasColumnName("City")
                .HasMaxLength(100);

            address.Property(a => a.PostalCode)
                .HasColumnName("PostalCode")
                .HasMaxLength(20);
        });

        // Auditable properties
        builder.Property(f => f.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(f => f.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(f => f.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        builder.Property(f => f.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Relationships
        builder.HasOne(f => f.Location)
            .WithMany(l => l.Factories)
            .HasForeignKey(f => f.LocationId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(f => f.CreatedByUser)
            .WithMany()
            .HasForeignKey(f => f.CreatedByUserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        builder.HasOne(f => f.UpdatedByUser)
            .WithMany()
            .HasForeignKey(f => f.UpdatedByUserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(f => f.LocationId)
            .HasDatabaseName("IX_Factories_LocationId");

        // Ignore domain events
        builder.Ignore(f => f.DomainEvents);
    }
}
