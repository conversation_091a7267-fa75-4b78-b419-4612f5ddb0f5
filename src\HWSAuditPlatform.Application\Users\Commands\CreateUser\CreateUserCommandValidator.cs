using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Users;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Commands.CreateUser;

/// <summary>
/// Validator for CreateUserCommand
/// </summary>
public class CreateUserCommandValidator : AbstractValidator<CreateUserCommand>
{
    private readonly IApplicationDbContext _context;

    public CreateUserCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.Username)
            .NotEmpty().WithMessage("Username is required")
            .MaximumLength(256).WithMessage("Username must not exceed 256 characters")
            .MustAsync(BeUniqueUsername).WithMessage("Username already exists");

        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("First name is required")
            .MaximumLength(100).WithMessage("First name must not exceed 100 characters");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Last name is required")
            .MaximumLength(100).WithMessage("Last name must not exceed 100 characters");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Email must be a valid email address")
            .MaximumLength(256).WithMessage("Email must not exceed 256 characters")
            .MustAsync(BeUniqueEmail).WithMessage("Email already exists");

        RuleFor(x => x.Role)
            .IsInEnum().WithMessage("Role must be a valid user role");

        RuleFor(x => x.FactoryId)
            .MustAsync(BeValidFactory).WithMessage("Factory does not exist")
            .When(x => x.FactoryId.HasValue);

        RuleFor(x => x.AdObjectGuid)
            .MaximumLength(255).WithMessage("AD Object GUID must not exceed 255 characters")
            .MustAsync(BeUniqueAdObjectGuid).WithMessage("AD Object GUID already exists")
            .When(x => !string.IsNullOrEmpty(x.AdObjectGuid));
    }

    private async Task<bool> BeUniqueUsername(string username, CancellationToken cancellationToken)
    {
        return !await _context.Users.AnyAsync(u => u.Username == username, cancellationToken);
    }

    private async Task<bool> BeUniqueEmail(string email, CancellationToken cancellationToken)
    {
        return !await _context.Users.AnyAsync(u => u.Email == email, cancellationToken);
    }

    private async Task<bool> BeValidFactory(int? factoryId, CancellationToken cancellationToken)
    {
        if (!factoryId.HasValue) return true;
        return await _context.Factories.AnyAsync(f => f.Id == factoryId.Value && f.IsActive, cancellationToken);
    }

    private async Task<bool> BeUniqueAdObjectGuid(string? adObjectGuid, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(adObjectGuid)) return true;
        return !await _context.Users.AnyAsync(u => u.AdObjectGuid == adObjectGuid, cancellationToken);
    }
}
