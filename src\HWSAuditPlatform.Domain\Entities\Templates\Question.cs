using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Templates;

/// <summary>
/// Represents a question within an audit template.
/// Maps to the Questions table in the database.
/// </summary>
public class Question : BaseEntity<int>
{
    /// <summary>
    /// Links to the audit template this question belongs to
    /// </summary>
    public int AuditTemplateId { get; set; }

    /// <summary>
    /// Navigation property for the audit template
    /// </summary>
    public virtual AuditTemplate AuditTemplate { get; set; } = null!;

    /// <summary>
    /// Optional: Links to a question group for organization
    /// </summary>
    public int? QuestionGroupId { get; set; }

    /// <summary>
    /// Navigation property for the question group
    /// </summary>
    public virtual QuestionGroup? QuestionGroup { get; set; }

    /// <summary>
    /// The actual text of the question. Candidate for multi-language support.
    /// </summary>
    [Required]
    public string QuestionText { get; set; } = string.Empty;

    /// <summary>
    /// The type of answer expected (e.g., YesNo, Numeric)
    /// </summary>
    [Required]
    public QuestionType QuestionType { get; set; }

    /// <summary>
    /// Order in which this question appears within its group/template
    /// </summary>
    public int DisplayOrder { get; set; } = 0;

    /// <summary>
    /// If true, the auditor must provide an answer or mark as Not Applicable
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// Optional weight for this question if the audit uses weighted scoring
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal? Weight { get; set; }

    /// <summary>
    /// Additional guidance or explanation for answering the question. Candidate for multi-language support.
    /// </summary>
    public string? HelpText { get; set; }

    /// <summary>
    /// For creating conditional follow-up questions; links to the parent question
    /// </summary>
    public int? ParentQuestionId { get; set; }

    /// <summary>
    /// Navigation property for the parent question
    /// </summary>
    public virtual Question? ParentQuestion { get; set; }

    /// <summary>
    /// Navigation property for child questions
    /// </summary>
    public virtual ICollection<Question> ChildQuestions { get; set; } = new List<Question>();

    /// <summary>
    /// Specific answer value from ParentQuestionId that triggers this conditional question
    /// </summary>
    [MaxLength(255)]
    public string? TriggerAnswerValue { get; set; }

    /// <summary>
    /// Default severity level to be applied if this question results in a failure or negative answer
    /// </summary>
    public SeverityLevel? SeverityLevel { get; set; }

    /// <summary>
    /// Indicates if evidence must be provided for this question
    /// </summary>
    public bool EvidenceRequired { get; set; } = false;

    /// <summary>
    /// Specific instructions for the required evidence. Candidate for multi-language support.
    /// </summary>
    public string? EvidenceInstructions { get; set; }

    /// <summary>
    /// Guidance on when the evidence should ideally be collected
    /// </summary>
    public EvidenceTimingHint? EvidenceTimingHint { get; set; }

    /// <summary>
    /// Comma-separated list of allowed evidence types (e.g., "image/jpeg", "application/pdf", "video/mp4").
    /// TODO: Consider normalizing to EvidenceTypes & QuestionAllowedEvidenceTypes tables.
    /// </summary>
    public string? AllowedEvidenceTypes { get; set; }

    /// <summary>
    /// Navigation property for predefined answer options (for SingleSelect/MultiSelect types)
    /// </summary>
    public virtual ICollection<QuestionOption> Options { get; set; } = new List<QuestionOption>();

    /// <summary>
    /// Indicates if this question is conditional (has a parent question)
    /// </summary>
    public bool IsConditional => ParentQuestionId.HasValue;

    /// <summary>
    /// Indicates if this question has child questions
    /// </summary>
    public bool HasChildQuestions => ChildQuestions.Any();
}
