# HWS Audit Platform - Developer Onboarding Guide

Welcome to the HWS Audit Platform development team! This comprehensive guide will help you get up and running with the project quickly and efficiently.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture Overview](#architecture-overview)
3. [Development Environment Setup](#development-environment-setup)
4. [Codebase Structure](#codebase-structure)
5. [Key Technical Concepts](#key-technical-concepts)
6. [Development Workflow](#development-workflow)
7. [Common Tasks](#common-tasks)
8. [Troubleshooting](#troubleshooting)
9. [Resources](#resources)

## Project Overview

### What is the HWS Audit Platform?

The HWS Audit Platform is a comprehensive audit management system designed for manufacturing quality audits with enterprise-scale requirements. The platform enables organizations to:

- **Conduct Digital Audits**: Replace paper-based audits with digital forms and workflows
- **Work Offline**: Progressive Web App (PWA) capabilities for auditing in areas with poor connectivity
- **Manage Users**: Seamless integration with Active Directory for enterprise user management
- **Track Progress**: Real-time audit progress tracking and reporting
- **Ensure Compliance**: Comprehensive audit trails and data retention policies

### Key Business Objectives

- **Improve Audit Efficiency**: Reduce audit completion time by 50% through digital workflows
- **Enhance Data Quality**: Eliminate transcription errors and ensure consistent data collection
- **Enable Offline Operations**: Support auditing in manufacturing environments with limited connectivity
- **Provide Real-time Insights**: Instant visibility into audit progress and results
- **Ensure Compliance**: Meet regulatory requirements with comprehensive audit trails

### Target Users

- **Auditors**: Conduct audits using mobile devices and tablets
- **Audit Managers**: Oversee audit programs and review results
- **System Administrators**: Manage users, templates, and system configuration
- **Quality Managers**: Analyze audit data and generate reports

## Architecture Overview

### High-Level System Architecture

The HWS Audit Platform follows Clean Architecture principles with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│              (API Controllers, Blazor Web)                  │
├─────────────────────────────────────────────────────────────┤
│                   Application Layer                         │
│           (Use Cases, Commands, Queries, DTOs)              │
├─────────────────────────────────────────────────────────────┤
│                     Domain Layer                            │
│         (Entities, Value Objects, Business Rules)           │
├─────────────────────────────────────────────────────────────┤
│                 Infrastructure Layer                        │
│        (Database, File Storage, External Services)          │
└─────────────────────────────────────────────────────────────┘
```

### Layer Responsibilities

- **Domain Layer**: Contains business entities, rules, and domain logic (no dependencies)
- **Application Layer**: Orchestrates business workflows using CQRS pattern (depends only on Domain)
- **Infrastructure Layer**: Implements external concerns like database access and file storage
- **Presentation Layer**: Handles HTTP requests, authentication, and user interfaces

### Dual Database Structure

The platform uses two separate databases:

1. **Main Business Database (HWSAP_DB)**: Stores core business entities (Users, Audits, Templates, etc.)
2. **Support Database**: Handles application runtime data (API keys, events, caching, feature flags, telemetry)

This separation ensures that application support features don't interfere with core business operations.

## Development Environment Setup

### Prerequisites

Before you begin, ensure you have the following installed:

- **.NET 10 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/10.0)
- **Visual Studio 2024** or **VS Code** with C# extension
- **SQL Server** - LocalDB, Express, or full version
- **Git** - For version control

### Step-by-Step Setup

#### 1. Clone the Repository

```bash
git clone local github repo
cd hws-audit-platform
```

#### 2. Restore NuGet Packages

```bash
dotnet restore
```

#### 3. Configure Database Connection

Create or update `src/HWSAuditPlatform.ApiService/appsettings.Development.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true;MultipleActiveResultSets=true",
    "SupportConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformSupport_Dev;Trusted_Connection=true;MultipleActiveResultSets=true"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  }
}
```

#### 4. Configure File Storage

For local development, add to your `appsettings.Development.json`:

```json
{
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads",
    "BaseUrl": "https://localhost:5001"
  }
}
```

#### 5. Set Up User Secrets (Optional)

For sensitive configuration:

```bash
dotnet user-secrets init --project src/HWSAuditPlatform.ApiService
dotnet user-secrets set "ConnectionStrings:DefaultConnection" "your-connection-string" --project src/HWSAuditPlatform.ApiService
```

#### 6. Create and Update Database

```bash
# Create main database migration
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# Create support database (if migrations exist)
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService --context SupportDbContext
```

#### 7. Run the Application

```bash
# Option 1: Run API Service directly
dotnet run --project src/HWSAuditPlatform.ApiService

# Option 2: Use AppHost for orchestrated startup
dotnet run --project src/HWSAuditPlatform.AppHost
```

#### 8. Verify Setup

- API Documentation (Scalar UI): `http://localhost:5375/scalar/v1`
- Swagger UI (fallback): `http://localhost:5375/swagger`
- Health Check: `http://localhost:5375/health`

### Optional: Active Directory Configuration

For AD integration testing (optional in development):

```json
{
  "ActiveDirectory": {
    "Domain": "dev.local",
    "Username": "<EMAIL>",
    "Password": "dev-password",
    "SearchBase": "OU=Users,DC=dev,DC=local"
  }
}
```

## Codebase Structure

### Solution Structure

```
HWSAuditPlatform/
├── src/
│   ├── HWSAuditPlatform.Domain/          # Business entities and rules
│   ├── HWSAuditPlatform.Application/     # Use cases and orchestration
│   ├── HWSAuditPlatform.Infrastructure/  # External concerns
│   ├── HWSAuditPlatform.ApiService/      # Web API controllers
│   ├── HWSAuditPlatform.Web/            # Blazor web application
│   ├── HWSAuditPlatform.AppHost/        # .NET Aspire orchestration
│   ├── HWSAuditPlatform.ServiceDefaults/ # Shared service configuration
│   ├── HWSAuditPlatform.SchedulerWorker/ # Background services
│   └── HWSAuditPlatform.Tests/          # Unit and integration tests
├── docs/                                 # Documentation
└── Directory.Packages.props             # Centralized package management
```

### Key Components and Responsibilities

#### Domain Layer (`HWSAuditPlatform.Domain`)

- **Entities**: Core business objects (User, Audit, AuditTemplate, etc.)
- **Value Objects**: Immutable objects without identity (Address, AuditStatus)
- **Domain Events**: Business events that trigger side effects
- **Enumerations**: Business-specific enums and constants
- **Interfaces**: Contracts for external dependencies

#### Application Layer (`HWSAuditPlatform.Application`)

- **Commands**: Write operations (CreateAudit, UpdateUser)
- **Queries**: Read operations (GetAudits, GetUserById)
- **Handlers**: Process commands and queries using MediatR
- **DTOs**: Data transfer objects for API communication
- **Validators**: Input validation using FluentValidation
- **Behaviors**: Cross-cutting concerns (logging, validation, caching)

#### Infrastructure Layer (`HWSAuditPlatform.Infrastructure`)

- **Persistence**: Entity Framework configurations and repositories
- **External Services**: Active Directory, file storage, email
- **Migrations**: Database schema changes
- **Health Checks**: System dependency monitoring

#### API Service (`HWSAuditPlatform.ApiService`)

- **Controllers**: HTTP request/response handling
- **Authentication**: JWT token validation and authorization
- **Middleware**: Request processing pipeline
- **Configuration**: Dependency injection and service registration

### Domain-Driven Design Approach

The project follows DDD principles:

- **Aggregates**: Consistency boundaries for related entities
- **Repositories**: Abstract data access patterns
- **Domain Services**: Complex business logic that doesn't belong to a single entity
- **Domain Events**: Decouple domain logic from side effects

### Testing Structure

Tests are organized by layer and type:

```
src/HWSAuditPlatform.Tests/
├── Domain/                    # Domain entity and value object tests
├── Application/               # Command/query handler tests
├── Infrastructure/            # Repository and service tests
├── Integration/               # End-to-end API tests
└── Common/                    # Shared test utilities
```

### Testing Conventions

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test complete workflows with real dependencies
- **Test Naming**: `MethodName_Scenario_ExpectedResult`
- **Arrange-Act-Assert**: Clear test structure
- **FluentAssertions**: Readable test assertions

## Key Technical Concepts

### AD Sync Integration for User Management

All users in the HWS Audit Platform are synchronized from Active Directory:

- **No Offline Registration**: Users cannot register directly in the application
- **Automatic Sync**: Background service synchronizes users and groups from AD
- **Role Mapping**: AD groups are mapped to application roles (Admin, Manager, Auditor)
- **Authentication**: Users authenticate using their AD credentials

### Database Schema Overview

#### Main Business Database

- Stores core entities: Users, Audits, AuditTemplates, Factories, etc.
- Uses CUID (25-character) identifiers for all entities
- Implements soft delete and audit trails
- Optimized for business operations and reporting

#### Support Database (support_database.dbml)

- **API Keys**: User-generated API keys for programmatic access
- **Event Store**: Domain events and system events for auditing
- **Feature Flags**: Dynamic feature toggling and A/B testing
- **Telemetry**: Application usage analytics and performance metrics
- **Caching**: Application-level caching for performance
- **User Preferences**: UI settings and personalization data

### API Documentation with Scalar UI

The platform uses Scalar UI as the primary API documentation interface:

- **Modern Interface**: Clean, interactive documentation
- **Authentication Support**: Built-in JWT token authentication
- **Code Generation**: Automatic client code examples
- **Search Functionality**: Quick endpoint discovery
- **Access**: Available at `/scalar/v1` in development

### Offline-First Architecture

The platform supports offline operations through:

- **Progressive Web App (PWA)**: Installable web application
- **Local Storage**: Client-side data caching
- **Sync Mechanisms**: Conflict resolution and data synchronization
- **CUID Identifiers**: Client-generated unique IDs for offline creation

## Development Workflow

### Git Workflow and Branching Strategy

We follow a feature-branch workflow:

```
main (production-ready code)
├── develop (integration branch)
│   ├── feature/audit-templates
│   ├── feature/offline-sync
│   └── hotfix/critical-bug-fix
```

#### Branch Naming Conventions
- **Feature branches**: `feature/description-of-feature`
- **Bug fixes**: `bugfix/description-of-bug`
- **Hotfixes**: `hotfix/critical-issue`
- **Releases**: `release/v1.2.0`

#### Workflow Steps
1. Create feature branch from `develop`
2. Implement feature following coding standards
3. Write/update tests (minimum 80% coverage)
4. Create pull request to `develop`
5. Code review and approval required
6. Merge to `develop` after CI passes
7. Deploy to staging for testing
8. Merge to `main` for production release

### Code Review Process

All code changes require review:

#### Review Checklist
- [ ] Code follows established patterns and conventions
- [ ] Business logic is in the Domain layer
- [ ] Tests are included and passing
- [ ] Documentation is updated if needed
- [ ] No sensitive data in code or logs
- [ ] Performance considerations addressed
- [ ] Security implications reviewed

#### Review Guidelines
- **Be Constructive**: Provide specific, actionable feedback
- **Focus on Code**: Review the code, not the person
- **Ask Questions**: If something is unclear, ask for clarification
- **Suggest Improvements**: Offer alternative approaches when appropriate
- **Approve Promptly**: Don't let PRs sit idle

### Testing Requirements

#### Test Coverage Requirements
- **Minimum Coverage**: 80% overall
- **Domain Layer**: 95% coverage (critical business logic)
- **Application Layer**: 85% coverage (use cases)
- **Infrastructure Layer**: 70% coverage (external integrations)

#### Running Tests

```bash
# Run all tests
dotnet test

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test project
dotnet test src/HWSAuditPlatform.Tests/

# Run tests in watch mode
dotnet watch test --project src/HWSAuditPlatform.Tests/
```

#### Test Categories

- **Unit Tests**: Fast, isolated, no external dependencies
- **Integration Tests**: Test complete workflows with database
- **API Tests**: Test HTTP endpoints end-to-end
- **Performance Tests**: Load testing for critical paths

### Deployment Process

#### Development Environment

- **Automatic**: Deploys on every commit to `develop`
- **Database**: Automatic migrations applied
- **File Storage**: Local file system
- **Monitoring**: Basic logging and health checks

#### Staging Environment

- **Manual**: Deploy from `develop` branch for testing
- **Database**: Production-like data with migrations
- **File Storage**: Local Storage
- **Monitoring**: Full Application Insights integration

#### Production Environment

- **Manual**: Deploy from `main` branch only
- **Database**:  SQL Server Database with backup strategy
- **File Storage**: Local File Storage on Network
- **Monitoring**: Comprehensive monitoring and alerting

## Common Tasks

### Adding a New Entity

Follow this step-by-step process to add a new business entity:

#### 1. Domain Layer (Start Here)

Create the entity in `src/HWSAuditPlatform.Domain/Entities/`:

```csharp
public class Equipment : AuditableEntity<string>
{
    public string Name { get; private set; }
    public string SerialNumber { get; private set; }
    public string FactoryId { get; private set; }
    public EquipmentStatus Status { get; private set; }

    // Factory method for creation
    public static Equipment Create(string name, string serialNumber,
        string factoryId, string createdByUserId)
    {
        var equipment = new Equipment
        {
            Id = Cuid.Generate(),
            Name = name,
            SerialNumber = serialNumber,
            FactoryId = factoryId,
            Status = EquipmentStatus.Active
        };

        equipment.AddDomainEvent(new EquipmentCreatedEvent(equipment.Id));
        return equipment;
    }

    public void UpdateStatus(EquipmentStatus newStatus, string updatedByUserId)
    {
        if (Status != newStatus)
        {
            Status = newStatus;
            AddDomainEvent(new EquipmentStatusChangedEvent(Id, Status));
        }
    }
}
```

#### 2. Application Layer

Create DTOs in `src/HWSAuditPlatform.Application/Equipment/DTOs/`:

```csharp
public class EquipmentDto : AuditableDto<string>
{
    public string Name { get; set; }
    public string SerialNumber { get; set; }
    public string FactoryId { get; set; }
    public EquipmentStatus Status { get; set; }
}
```

Create commands and queries:

```csharp
// Commands/CreateEquipment/CreateEquipmentCommand.cs
public class CreateEquipmentCommand : BaseCommand<string>
{
    public string Name { get; set; }
    public string SerialNumber { get; set; }
    public string FactoryId { get; set; }
}

// Commands/CreateEquipment/CreateEquipmentCommandHandler.cs
public class CreateEquipmentCommandHandler : BaseCommandHandler<CreateEquipmentCommand, string>
{
    public override async Task<string> Handle(CreateEquipmentCommand request, CancellationToken cancellationToken)
    {
        var equipment = Equipment.Create(request.Name, request.SerialNumber,
            request.FactoryId, _currentUserService.UserId);

        await _context.Equipment.AddAsync(equipment, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        return equipment.Id;
    }
}

// Commands/CreateEquipment/CreateEquipmentCommandValidator.cs
public class CreateEquipmentCommandValidator : AbstractValidator<CreateEquipmentCommand>
{
    public CreateEquipmentCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .MaximumLength(200);

        RuleFor(x => x.SerialNumber)
            .NotEmpty()
            .MaximumLength(100);
    }
}
```

#### 3. Infrastructure Layer

Create entity configuration in `src/HWSAuditPlatform.Infrastructure/Persistence/Configurations/`:

```csharp
public class EquipmentConfiguration : IEntityTypeConfiguration<Equipment>
{
    public void Configure(EntityTypeBuilder<Equipment> builder)
    {
        builder.ToTable("Equipment");

        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id).HasMaxLength(25);

        builder.Property(e => e.Name).HasMaxLength(200).IsRequired();
        builder.Property(e => e.SerialNumber).HasMaxLength(100).IsRequired();
        builder.Property(e => e.Status).HasConversion<string>();

        builder.HasIndex(e => e.SerialNumber).IsUnique();
        builder.HasIndex(e => e.FactoryId);
    }
}
```

Add DbSet to ApplicationDbContext:

```csharp
public DbSet<Equipment> Equipment => Set<Equipment>();
```

#### 4. Create and Apply Migration

```bash
dotnet ef migrations add AddEquipmentEntity --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
dotnet ef database update --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService
```

#### 5. API Layer

Create controller in `src/HWSAuditPlatform.ApiService/Controllers/`:

```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class EquipmentController : ControllerBase
{
    private readonly IMediator _mediator;

    public EquipmentController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    public async Task<ActionResult<string>> Create(CreateEquipmentCommand command)
    {
        var equipmentId = await _mediator.Send(command);
        return CreatedAtAction(nameof(GetById), new { id = equipmentId }, equipmentId);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<EquipmentDto>> GetById(string id)
    {
        var query = new GetEquipmentByIdQuery { Id = id };
        var equipment = await _mediator.Send(query);
        return Ok(equipment);
    }
}
```

### Adding a New API Endpoint

To add a new endpoint to an existing controller:

#### 1. Create Query/Command

```csharp
public class GetEquipmentByFactoryQuery : BaseQuery<PaginatedResult<EquipmentDto>>
{
    public string FactoryId { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
}
```

#### 2. Create Handler

```csharp
public class GetEquipmentByFactoryQueryHandler : BaseQueryHandler<GetEquipmentByFactoryQuery, PaginatedResult<EquipmentDto>>
{
    public override async Task<PaginatedResult<EquipmentDto>> Handle(GetEquipmentByFactoryQuery request, CancellationToken cancellationToken)
    {
        var query = _context.Equipment
            .Where(e => e.FactoryId == request.FactoryId)
            .OrderBy(e => e.Name);

        return await query.ProjectTo<EquipmentDto>(_mapper.ConfigurationProvider)
            .ToPaginatedResultAsync(request.PageNumber, request.PageSize, cancellationToken);
    }
}
```

#### 3. Add Controller Action

```csharp
[HttpGet("factory/{factoryId}")]
public async Task<ActionResult<PaginatedResult<EquipmentDto>>> GetByFactory(
    string factoryId,
    [FromQuery] int pageNumber = 1,
    [FromQuery] int pageSize = 10)
{
    var query = new GetEquipmentByFactoryQuery
    {
        FactoryId = factoryId,
        PageNumber = pageNumber,
        PageSize = pageSize
    };

    var result = await _mediator.Send(query);
    return Ok(result);
}
```

### Database Migration Best Practices

#### Creating Migrations

```bash
# Always use descriptive names
dotnet ef migrations add AddEquipmentStatusIndex --project src/HWSAuditPlatform.Infrastructure --startup-project src/HWSAuditPlatform.ApiService

# Review the generated migration before applying
# Check: src/HWSAuditPlatform.Infrastructure/Migrations/
```

#### Data Migrations

For complex data transformations:

```csharp
protected override void Up(MigrationBuilder migrationBuilder)
{
    // Schema changes first
    migrationBuilder.AddColumn<string>(
        name: "NewColumn",
        table: "Equipment",
        nullable: true);

    // Data migration with raw SQL
    migrationBuilder.Sql(@"
        UPDATE Equipment
        SET NewColumn = CASE
            WHEN Status = 'Active' THEN 'Operational'
            WHEN Status = 'Inactive' THEN 'Maintenance'
            ELSE 'Unknown'
        END
    ");

    // Make column non-nullable after data migration
    migrationBuilder.AlterColumn<string>(
        name: "NewColumn",
        table: "Equipment",
        nullable: false,
        oldNullable: true);
}
```

### Writing and Running Tests

#### Unit Test Example

```csharp
public class EquipmentTests
{
    [Fact]
    public void Create_WithValidData_ShouldCreateEquipment()
    {
        // Arrange
        var name = "Test Equipment";
        var serialNumber = "SN123456";
        var factoryId = "factory-123";
        var createdBy = "user-123";

        // Act
        var equipment = Equipment.Create(name, serialNumber, factoryId, createdBy);

        // Assert
        equipment.Name.Should().Be(name);
        equipment.SerialNumber.Should().Be(serialNumber);
        equipment.FactoryId.Should().Be(factoryId);
        equipment.Status.Should().Be(EquipmentStatus.Active);
        equipment.DomainEvents.Should().ContainSingle(e => e is EquipmentCreatedEvent);
    }
}
```

#### Integration Test Example

```csharp
public class EquipmentControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public EquipmentControllerTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task CreateEquipment_WithValidData_ShouldReturnCreated()
    {
        // Arrange
        var command = new CreateEquipmentCommand
        {
            Name = "Test Equipment",
            SerialNumber = "SN123456",
            FactoryId = "factory-123"
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/equipment", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        var equipmentId = await response.Content.ReadAsStringAsync();
        equipmentId.Should().NotBeNullOrEmpty();
    }
}
```

## Troubleshooting

### Common Issues and Solutions

#### Database Connection Issues

**Problem**: Cannot connect to LocalDB
```
Microsoft.Data.SqlClient.SqlException: A network-related or instance-specific error occurred
```

**Solutions**:
1. Verify LocalDB is installed: `sqllocaldb info`
2. Start LocalDB instance: `sqllocaldb start mssqllocaldb`
3. Check connection string in `appsettings.Development.json`
4. Try alternative connection string: `Server=.;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true`

#### Migration Issues

**Problem**: Migration fails with constraint errors
```
The ALTER TABLE statement conflicted with the FOREIGN KEY constraint
```

**Solutions**:
1. Check entity relationships and foreign keys
2. Review migration order and dependencies
3. Use data migration to handle existing data
4. Consider dropping and recreating database in development

#### Package Restore Issues

**Problem**: NuGet packages fail to restore
```
error NU1101: Unable to find package
```

**Solutions**:
1. Clear NuGet cache: `dotnet nuget locals all --clear`
2. Restore packages: `dotnet restore --force`
3. Check package sources: `dotnet nuget list source`
4. Verify internet connectivity and proxy settings

#### Build Errors

**Problem**: Circular dependency errors
```
error CS0246: The type or namespace name could not be found
```

**Solutions**:
1. Check project references in `.csproj` files
2. Ensure proper layer dependencies (Domain → Application → Infrastructure)
3. Clean and rebuild solution: `dotnet clean && dotnet build`
4. Check for missing using statements

#### Authentication Issues

**Problem**: 401 Unauthorized when testing API
```
System.UnauthorizedAccessException: Unauthorized
```

**Solutions**:
1. Verify JWT token is valid and not expired
2. Check token format: `Bearer your-jwt-token`
3. Ensure user has required roles/permissions
4. Check Active Directory configuration
5. Review authentication middleware configuration

#### File Storage Issues

**Problem**: File upload fails
```
System.IO.DirectoryNotFoundException: Could not find a part of the path
```

**Solutions**:
1. Verify storage path exists: `wwwroot/uploads`
2. Check file permissions on storage directory
3. Verify file storage configuration in appsettings.json


### Performance Issues

#### Slow Database Queries

**Symptoms**: API responses taking > 2 seconds

**Debugging Steps**:
1. Enable EF Core query logging:
   ```json
   {
     "Logging": {
       "LogLevel": {
         "Microsoft.EntityFrameworkCore.Database.Command": "Information"
       }
     }
   }
   ```

2. Check for N+1 query problems
3. Add appropriate indexes
4. Use `Include()` for related data
5. Consider query optimization or caching

#### Memory Leaks

**Symptoms**: Application memory usage continuously increasing

**Debugging Steps**:
1. Use diagnostic tools: `dotnet-counters`, `dotnet-dump`
2. Check for undisposed resources
3. Review event handler subscriptions
4. Monitor garbage collection metrics
5. Use memory profilers (JetBrains dotMemory, PerfView)

### Getting Help

#### Internal Resources
1. **Team Chat**: #hws-audit-platform Slack channel
2. **Documentation**: Check `/docs` folder for detailed guides
3. **Code Reviews**: Ask team members for guidance
4. **Architecture Decisions**: Review ADR documents

#### External Resources
1. **Stack Overflow**: Tag questions with `asp.net-core`, `entity-framework-core`
2. **Microsoft Docs**: Official .NET and EF Core documentation
3. **GitHub Issues**: Check project repository for known issues
4. **Community Forums**: .NET Community forums and Discord

#### Escalation Process
1. **Level 1**: Ask team members or tech lead
2. **Level 2**: Consult with senior developers or architects
3. **Level 3**: Engage with external consultants or Microsoft support

## Resources

### Documentation Links
- **[Architecture Overview](docs/Architecture-Overview.md)**: Comprehensive system architecture guide
- **[Application Layer Documentation](docs/Application-Layer-Documentation.md)**: CQRS and MediatR patterns
- **[Infrastructure Layer Documentation](docs/Infrastructure-Layer-Documentation.md)**: Database and external services
- **[API Integration Guide](docs/API-Integration-Guide.md)**: How to use the APIs
- **[Developer Guide](docs/Developer-Guide.md)**: Detailed development workflows

### Database Schema
- **[Main Database Schema](docs/database.dbml)**: Core business entities
- **[Support Database Schema](docs/suppor_database.dbml)**: Application runtime data

### API Documentation
- **Scalar UI**: `http://localhost:5375/scalar/v1` (primary documentation)
- **Swagger UI**: `http://localhost:5375/swagger` (fallback)
- **OpenAPI Spec**: `http://localhost:5375/swagger/v1/swagger.json`

### Development Tools
- **Visual Studio 2024**: Primary IDE with full debugging support
- **SQL Server Management Studio**: Database management and query execution
- **Postman**: API testing and collection sharing
- **Git Extensions**: Visual Git client for Windows
- **LINQPad**: .NET code snippets and database queries

### Learning Resources
- **Clean Architecture**: [Uncle Bob's Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- **Domain-Driven Design**: [DDD Reference](https://www.domainlanguage.com/ddd/reference/)
- **CQRS Pattern**: [Microsoft CQRS Guide](https://docs.microsoft.com/en-us/dotnet/architecture/microservices/microservice-ddd-cqrs-patterns/apply-simplified-microservice-cqrs-ddd-patterns)
- **Entity Framework Core**: [EF Core Documentation](https://docs.microsoft.com/en-us/ef/core/)
- **ASP.NET Core**: [ASP.NET Core Documentation](https://docs.microsoft.com/en-us/aspnet/core/)

### Team Contacts
- **Tech Lead**: [Name] - [email] - Architecture and technical decisions
- **DevOps Engineer**: [Name] - [email] - CI/CD and infrastructure
- **QA Lead**: [Name] - [email] - Testing strategies and quality assurance
- **Product Owner**: [Name] - [email] - Business requirements and priorities
- **Scrum Master**: [Name] - [email] - Process and team coordination

### Code Standards and Conventions
- **C# Coding Standards**: Follow Microsoft's C# coding conventions
- **Naming Conventions**: PascalCase for public members, camelCase for private fields
- **File Organization**: One class per file, organized by feature
- **Documentation**: XML comments for public APIs
- **Git Commit Messages**: Use conventional commit format

---

## Welcome to the Team!

You're now ready to start contributing to the HWS Audit Platform! Remember:

1. **Start Small**: Begin with small bug fixes or documentation improvements
2. **Ask Questions**: Don't hesitate to ask for help or clarification
3. **Follow Patterns**: Study existing code to understand established patterns
4. **Write Tests**: Always include tests with your changes
5. **Document Changes**: Update documentation when adding new features

Happy coding! 🚀

