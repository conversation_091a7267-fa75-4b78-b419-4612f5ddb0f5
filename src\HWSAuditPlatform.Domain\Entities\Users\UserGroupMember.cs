using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Domain.Entities.Users;

/// <summary>
/// Represents the many-to-many relationship between users and user groups.
/// Maps to the UserGroupMembers table in the database.
/// </summary>
public class UserGroupMember : BaseEntity
{
    /// <summary>
    /// Links to UserGroups (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string UserGroupId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the user group
    /// </summary>
    public virtual UserGroup UserGroup { get; set; } = null!;

    /// <summary>
    /// Links to Users (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the user
    /// </summary>
    public virtual User User { get; set; } = null!;
}
