using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Findings;

/// <summary>
/// Represents a finding/non-conformity identified during an audit.
/// Maps to the Findings table in the database.
/// </summary>
public class Finding : AuditableEntity<string>, IAggregateRoot
{
    /// <summary>
    /// Links to the specific audit answer that primarily generated this finding (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string AuditAnswerId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the audit answer
    /// </summary>
    public virtual AuditAnswer AuditAnswer { get; set; } = null!;

    /// <summary>
    /// A human-readable unique identifier for the finding (e.g., FND-2025-001), potentially system-generated
    /// </summary>
    [MaxLength(50)]
    public string? FindingCode { get; set; }

    /// <summary>
    /// Detailed description of the non-conformity. Candidate for multi-language support.
    /// </summary>
    [Required]
    public string FindingDescription { get; set; } = string.Empty;

    /// <summary>
    /// Severity level assigned to this finding
    /// </summary>
    public SeverityLevel FindingSeverityLevel { get; set; }

    /// <summary>
    /// Analysis of the root cause of the finding
    /// </summary>
    public string? RootCauseAnalysis { get; set; }

    /// <summary>
    /// Description of any immediate actions taken upon discovering the finding
    /// </summary>
    public string? ImmediateActionTaken { get; set; }

    /// <summary>
    /// Current status of the finding in its lifecycle
    /// </summary>
    public FindingStatus Status { get; set; } = FindingStatus.Open;

    /// <summary>
    /// User who reported or created this finding (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string ReportedByUserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the user who reported the finding
    /// </summary>
    public virtual User ReportedByUser { get; set; } = null!;

    /// <summary>
    /// Target date for resolving this finding and its corrective actions
    /// </summary>
    public DateOnly? DueDate { get; set; }

    /// <summary>
    /// Navigation property for corrective actions associated with this finding
    /// </summary>
    public virtual ICollection<CorrectiveAction> CorrectiveActions { get; set; } = new List<CorrectiveAction>();

    /// <summary>
    /// Indicates if the finding is overdue
    /// </summary>
    public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateOnly.FromDateTime(DateTime.UtcNow) && Status != FindingStatus.Closed;

    /// <summary>
    /// Indicates if the finding is open
    /// </summary>
    public bool IsOpen => Status == FindingStatus.Open;

    /// <summary>
    /// Indicates if the finding is closed
    /// </summary>
    public bool IsClosed => Status == FindingStatus.Closed;

    /// <summary>
    /// Gets the number of corrective actions
    /// </summary>
    public int CorrectiveActionCount => CorrectiveActions.Count;

    /// <summary>
    /// Gets the number of open corrective actions
    /// </summary>
    public int OpenCorrectiveActionCount => CorrectiveActions.Count(ca => ca.Status != CorrectiveActionStatus.VerifiedClosed && ca.Status != CorrectiveActionStatus.Cancelled);
}
