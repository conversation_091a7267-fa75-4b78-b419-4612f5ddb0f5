graph TD
    subgraph User Interfaces
        PWA[<PERSON><PERSON><PERSON> PWA, Auditor Field App]
        Portal[Blazor Server, Management Portal]
    end

    subgraph Backend Services
        API[Web API, e.g., ASP.NET Core]
        Scheduler[SchedulerWorker, Background Service]
    end

    subgraph Core Logic
        AppLayer[Application Layer]
        DomainLayer[Domain Layer]
    end

    subgraph Data & External Services
        DB[HWSAP DB - SQL Server]      
        ADInt[Active Directory]
        LocalFS[Local File System, for Attachments] 
        ExtOrgDB[(Future: External Org DB)]        
        ExtAM[(Future: Action Manager - AM)]        
    end

    PWA -->|HTTPS| API
    Portal -->|HTTPS/SignalR| API
    API --> AppLayer
    Scheduler --> AppLayer
    AppLayer --> DomainLayer
    AppLayer -->|Data Access| InfraLayer
    InfraLayer --> DB
    InfraLayer --> ADInt
    InfraLayer --> LocalFS             
    InfraLayer -.->|Future Integration| ExtOrgDB   
    AppLayer -.->|Future Integration| ExtAM   