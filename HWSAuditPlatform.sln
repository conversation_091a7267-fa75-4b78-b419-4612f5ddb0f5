Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36121.58
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HWSAuditPlatform.ApiService", "src\HWSAuditPlatform.ApiService\HWSAuditPlatform.ApiService.csproj", "{EBC34F72-FFEC-40BD-A5E0-9F3BBCE4F17E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HWSAuditPlatform.AppHost", "src\HWSAuditPlatform.AppHost\HWSAuditPlatform.AppHost.csproj", "{D9E56140-2BBC-4DBC-2B05-6641671E47FF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HWSAuditPlatform.ServiceDefaults", "src\HWSAuditPlatform.ServiceDefaults\HWSAuditPlatform.ServiceDefaults.csproj", "{63700901-D11E-5A45-3433-8E12E08C763E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HWSAuditPlatform.Tests", "src\HWSAuditPlatform.Tests\HWSAuditPlatform.Tests.csproj", "{5B361556-D30E-1839-DA36-64306E81116C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HWSAuditPlatform.Web", "src\HWSAuditPlatform.Web\HWSAuditPlatform.Web.csproj", "{134F2087-8A6C-D577-281F-1AC20F07E8B7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HWSAuditPlatform.Domain", "src\HWSAuditPlatform.Domain\HWSAuditPlatform.Domain.csproj", "{7F684C50-2819-48B1-B9D8-C4CB547D9F61}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HWSAuditPlatform.Infrastructure", "src\HWSAuditPlatform.Infrastructure\HWSAuditPlatform.Infrastructure.csproj", "{D38D45BE-17FD-C2DD-5135-4F191E9A7719}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HWSAuditPlatform.Application", "src\HWSAuditPlatform.Application\HWSAuditPlatform.Application.csproj", "{9F66E5C2-91C1-9D98-D6A9-6A7F37142AD0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HWSAuditPlatform.SchedulerWorker", "src\HWSAuditPlatform.SchedulerWorker\HWSAuditPlatform.SchedulerWorker.csproj", "{B2FC44B3-1F54-EC4A-F2FE-8F95F7CA51F7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{EBC34F72-FFEC-40BD-A5E0-9F3BBCE4F17E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EBC34F72-FFEC-40BD-A5E0-9F3BBCE4F17E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EBC34F72-FFEC-40BD-A5E0-9F3BBCE4F17E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EBC34F72-FFEC-40BD-A5E0-9F3BBCE4F17E}.Release|Any CPU.Build.0 = Release|Any CPU
		{D9E56140-2BBC-4DBC-2B05-6641671E47FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D9E56140-2BBC-4DBC-2B05-6641671E47FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D9E56140-2BBC-4DBC-2B05-6641671E47FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D9E56140-2BBC-4DBC-2B05-6641671E47FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{63700901-D11E-5A45-3433-8E12E08C763E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63700901-D11E-5A45-3433-8E12E08C763E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63700901-D11E-5A45-3433-8E12E08C763E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63700901-D11E-5A45-3433-8E12E08C763E}.Release|Any CPU.Build.0 = Release|Any CPU
		{5B361556-D30E-1839-DA36-64306E81116C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5B361556-D30E-1839-DA36-64306E81116C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5B361556-D30E-1839-DA36-64306E81116C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5B361556-D30E-1839-DA36-64306E81116C}.Release|Any CPU.Build.0 = Release|Any CPU
		{134F2087-8A6C-D577-281F-1AC20F07E8B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{134F2087-8A6C-D577-281F-1AC20F07E8B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{134F2087-8A6C-D577-281F-1AC20F07E8B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{134F2087-8A6C-D577-281F-1AC20F07E8B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F684C50-2819-48B1-B9D8-C4CB547D9F61}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F684C50-2819-48B1-B9D8-C4CB547D9F61}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F684C50-2819-48B1-B9D8-C4CB547D9F61}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F684C50-2819-48B1-B9D8-C4CB547D9F61}.Release|Any CPU.Build.0 = Release|Any CPU
		{D38D45BE-17FD-C2DD-5135-4F191E9A7719}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D38D45BE-17FD-C2DD-5135-4F191E9A7719}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D38D45BE-17FD-C2DD-5135-4F191E9A7719}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D38D45BE-17FD-C2DD-5135-4F191E9A7719}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F66E5C2-91C1-9D98-D6A9-6A7F37142AD0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F66E5C2-91C1-9D98-D6A9-6A7F37142AD0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F66E5C2-91C1-9D98-D6A9-6A7F37142AD0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F66E5C2-91C1-9D98-D6A9-6A7F37142AD0}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2FC44B3-1F54-EC4A-F2FE-8F95F7CA51F7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2FC44B3-1F54-EC4A-F2FE-8F95F7CA51F7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2FC44B3-1F54-EC4A-F2FE-8F95F7CA51F7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2FC44B3-1F54-EC4A-F2FE-8F95F7CA51F7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {71E3B698-21DD-4289-947C-D7F003E2E117}
	EndGlobalSection
EndGlobal
