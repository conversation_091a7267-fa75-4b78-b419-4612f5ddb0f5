using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Audits.Commands.CreateAudit;

/// <summary>
/// Validator for CreateAuditCommand
/// </summary>
public class CreateAuditCommandValidator : AbstractValidator<CreateAuditCommand>
{
    private readonly IApplicationDbContext _context;

    public CreateAuditCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.AuditTemplateId)
            .GreaterThan(0).WithMessage("Audit template ID must be greater than 0")
            .MustAsync(BeValidAuditTemplate).WithMessage("Audit template does not exist or is not published");

        RuleFor(x => x.AssignmentType)
            .IsInEnum().WithMessage("Assignment type must be valid");

        RuleFor(x => x.AssignedToUserId)
            .NotEmpty().WithMessage("Assigned user is required for individual assignment")
            .MustAsync(BeValidUser).WithMessage("Assigned user does not exist or is not active")
            .When(x => x.AssignmentType == AssignmentType.Individual);

        RuleFor(x => x.AssignedToUserGroupId)
            .NotEmpty().WithMessage("Assigned user group is required for group assignment")
            .MustAsync(BeValidUserGroup).WithMessage("Assigned user group does not exist")
            .When(x => x.AssignmentType == AssignmentType.GroupAny);

        RuleFor(x => x.ScheduledDate)
            .NotEmpty().WithMessage("Scheduled date is required");

        RuleFor(x => x.DueDate)
            .GreaterThan(x => x.ScheduledDate).WithMessage("Due date must be after scheduled date")
            .When(x => x.DueDate.HasValue);

        RuleFor(x => x.FactoryId)
            .GreaterThan(0).WithMessage("Factory ID must be greater than 0")
            .MustAsync(BeValidFactory).WithMessage("Factory does not exist or is not active");

        RuleFor(x => x.AreaId)
            .GreaterThan(0).WithMessage("Area ID must be greater than 0")
            .MustAsync(BeValidArea).WithMessage("Area does not exist or is not active");

        RuleFor(x => x.SubAreaId)
            .MustAsync(BeValidSubArea).WithMessage("Sub-area does not exist or is not active")
            .When(x => x.SubAreaId.HasValue);

        RuleFor(x => x)
            .MustAsync(BeValidAreaFactoryRelationship).WithMessage("Area does not belong to the specified factory");

        RuleFor(x => x)
            .MustAsync(BeValidSubAreaAreaRelationship).WithMessage("Sub-area does not belong to the specified area")
            .When(x => x.SubAreaId.HasValue);
    }

    private async Task<bool> BeValidAuditTemplate(int auditTemplateId, CancellationToken cancellationToken)
    {
        return await _context.AuditTemplates.AnyAsync(
            t => t.Id == auditTemplateId && t.IsPublished && t.IsActive, 
            cancellationToken);
    }

    private async Task<bool> BeValidUser(string? userId, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(userId)) return true;
        return await _context.Users.AnyAsync(u => u.Id == userId && u.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidUserGroup(string? userGroupId, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(userGroupId)) return true;
        return await _context.UserGroups.AnyAsync(g => g.Id == userGroupId, cancellationToken);
    }

    private async Task<bool> BeValidFactory(int factoryId, CancellationToken cancellationToken)
    {
        return await _context.Factories.AnyAsync(f => f.Id == factoryId && f.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidArea(int areaId, CancellationToken cancellationToken)
    {
        return await _context.Areas.AnyAsync(a => a.Id == areaId && a.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidSubArea(int? subAreaId, CancellationToken cancellationToken)
    {
        if (!subAreaId.HasValue) return true;
        return await _context.SubAreas.AnyAsync(sa => sa.Id == subAreaId.Value && sa.IsActive, cancellationToken);
    }

    private async Task<bool> BeValidAreaFactoryRelationship(CreateAuditCommand command, CancellationToken cancellationToken)
    {
        return await _context.Areas.AnyAsync(a => a.Id == command.AreaId && a.FactoryId == command.FactoryId, cancellationToken);
    }

    private async Task<bool> BeValidSubAreaAreaRelationship(CreateAuditCommand command, CancellationToken cancellationToken)
    {
        if (!command.SubAreaId.HasValue) return true;
        return await _context.SubAreas.AnyAsync(sa => sa.Id == command.SubAreaId.Value && sa.AreaId == command.AreaId, cancellationToken);
    }
}
