using System.Diagnostics;
using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for basic health and status checks
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class HealthController : BaseController
{
    public HealthController(IMediator mediator, ILogger<HealthController> logger) 
        : base(mediator, logger)
    {
    }

    /// <summary>
    /// Basic health check endpoint
    /// </summary>
    /// <returns>Health status</returns>
    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult<object> GetHealth()
    {
        var healthInfo = new
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0",
            Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId,
            WorkingSet = GC.GetTotalMemory(false),
            Uptime = DateTime.UtcNow - Process.GetCurrentProcess().StartTime.ToUniversalTime()
        };

        Logger.LogInformation("Health check requested");
        return Success(healthInfo, "API is healthy");
    }

    /// <summary>
    /// API information endpoint
    /// </summary>
    /// <returns>API information</returns>
    [HttpGet("info")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult<object> GetInfo()
    {
        var apiInfo = new
        {
            Name = "HWS Audit Platform API",
            Version = "1.0.0",
            Description = "Manufacturing Quality Audit Management System API",
            Documentation = "/swagger",
            HealthCheck = "/health",
            Endpoints = new
            {
                Authentication = "/api/v1/auth",
                Users = "/api/v1/users",
                Audits = "/api/v1/audits",
                Organization = "/api/v1/organization",
                Files = "/api/v1/files"
            },
            Features = new[]
            {
                "JWT Authentication",
                "Active Directory Integration",
                "Role-based Authorization",
                "File Upload/Download",
                "Audit Management",
                "Organization Hierarchy",
                "Health Monitoring"
            }
        };

        return Success(apiInfo, "API information retrieved successfully");
    }

    /// <summary>
    /// Test endpoint for connectivity
    /// </summary>
    /// <returns>Test response</returns>
    [HttpGet("ping")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    public ActionResult<object> Ping()
    {
        var response = new
        {
            Message = "Pong",
            Timestamp = DateTime.UtcNow,
            RequestId = HttpContext.TraceIdentifier
        };

        return Success(response, "Ping successful");
    }

    /// <summary>
    /// Test authenticated endpoint
    /// </summary>
    /// <returns>Authenticated test response</returns>
    [HttpGet("auth-test")]
    [Authorize]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(401)]
    public ActionResult<object> AuthTest()
    {
        var response = new
        {
            Message = "Authentication successful",
            User = new
            {
                Id = GetCurrentUserId(),
                Username = GetCurrentUsername(),
                Role = GetCurrentUserRole(),
                FactoryId = GetCurrentUserFactoryId()
            },
            Timestamp = DateTime.UtcNow
        };

        Logger.LogInformation("Authenticated test endpoint accessed by user: {Username}", GetCurrentUsername());
        return Success(response, "Authentication test successful");
    }
}
