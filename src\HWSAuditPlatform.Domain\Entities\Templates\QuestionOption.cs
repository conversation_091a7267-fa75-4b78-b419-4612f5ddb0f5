using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Domain.Entities.Templates;

/// <summary>
/// Represents a predefined answer option for SingleSelect/MultiSelect questions.
/// Maps to the QuestionOptions table in the database.
/// </summary>
public class QuestionOption : BaseEntity<int>
{
    /// <summary>
    /// Links to the question these options belong to (for SingleSelect/MultiSelect types)
    /// </summary>
    public int QuestionId { get; set; }

    /// <summary>
    /// Navigation property for the question
    /// </summary>
    public virtual Question Question { get; set; } = null!;

    /// <summary>
    /// Display text for the answer option. Candidate for multi-language support.
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string OptionText { get; set; } = string.Empty;

    /// <summary>
    /// Internal value of the option, can be used for scoring or as TriggerAnswerValue for conditional logic
    /// </summary>
    [MaxLength(100)]
    public string? OptionValue { get; set; }

    /// <summary>
    /// Indicates if this is the "correct" option, if applicable (e.g., for quiz-like audits)
    /// </summary>
    public bool? IsCorrectOption { get; set; }

    /// <summary>
    /// Order in which this option appears for the question
    /// </summary>
    public int DisplayOrder { get; set; } = 0;
}
