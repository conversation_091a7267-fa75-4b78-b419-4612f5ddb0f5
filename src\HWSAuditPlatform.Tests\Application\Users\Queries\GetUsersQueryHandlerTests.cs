using FluentAssertions;
using HWSAuditPlatform.Application.Users.Queries.GetUsers;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Application.Users.Queries;

public class GetUsersQueryHandlerTests : BaseDbTestClass
{
    private readonly GetUsersQueryHandler _handler;

    public GetUsersQueryHandlerTests()
    {
        _handler = new GetUsersQueryHandler(Context);
    }

    [Fact]
    public async Task Handle_WithDefaultQuery_ShouldReturnAllUsers()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUsersQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(3); // admin, manager, auditor
        result.TotalCount.Should().Be(3);
        result.PageNumber.Should().Be(1);
        result.PageSize.Should().Be(10);
        result.TotalPages.Should().Be(1);
    }

    [Fact]
    public async Task Handle_WithSearchTerm_ShouldFilterUsers()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUsersQuery
        {
            SearchTerm = "admin",
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(1);
        result.Items.First().Username.Should().Be("admin");
        result.TotalCount.Should().Be(1);
    }

    [Fact]
    public async Task Handle_WithRoleFilter_ShouldFilterByRole()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUsersQuery
        {
            Role = UserRole.Auditor,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(1);
        result.Items.First().Role.Should().Be(UserRole.Auditor);
        result.TotalCount.Should().Be(1);
    }

    [Fact]
    public async Task Handle_WithIsActiveFilter_ShouldFilterByActiveStatus()
    {
        // Arrange
        await SeedTestDataAsync();
        
        // Deactivate one user
        var userToDeactivate = Context.Users.First(u => u.Username == "auditor");
        userToDeactivate.IsActive = false;
        await Context.SaveChangesAsync();
        
        var query = new GetUsersQuery
        {
            IsActive = true,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(2); // Only active users
        result.Items.Should().OnlyContain(u => u.IsActive);
        result.TotalCount.Should().Be(2);
    }

    [Fact]
    public async Task Handle_WithFactoryIdFilter_ShouldFilterByFactory()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUsersQuery
        {
            FactoryId = 1,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(3); // All test users are in factory 1
        result.TotalCount.Should().Be(3);
    }

    [Fact]
    public async Task Handle_WithPagination_ShouldReturnCorrectPage()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUsersQuery
        {
            PageNumber = 2,
            PageSize = 2
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(1); // Last user on page 2
        result.TotalCount.Should().Be(3);
        result.PageNumber.Should().Be(2);
        result.PageSize.Should().Be(2);
        result.TotalPages.Should().Be(2);
    }

    [Fact]
    public async Task Handle_WithSortByUsername_ShouldSortCorrectly()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUsersQuery
        {
            SortBy = "Username",
            SortDescending = false,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(3);
        result.Items.Should().BeInAscendingOrder(u => u.Username);
    }

    [Fact]
    public async Task Handle_WithSortByUsernameDescending_ShouldSortCorrectly()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUsersQuery
        {
            SortBy = "Username",
            SortDescending = true,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(3);
        result.Items.Should().BeInDescendingOrder(u => u.Username);
    }

    [Fact]
    public async Task Handle_WithMultipleFilters_ShouldApplyAllFilters()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUsersQuery
        {
            SearchTerm = "user", // Should match "Manager User" and "Auditor User"
            Role = UserRole.Manager,
            IsActive = true,
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(1);
        result.Items.First().Role.Should().Be(UserRole.Manager);
        result.Items.First().IsActive.Should().BeTrue();
        result.TotalCount.Should().Be(1);
    }

    [Fact]
    public async Task Handle_ShouldReturnCorrectUserSummaryData()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var query = new GetUsersQuery
        {
            PageNumber = 1,
            PageSize = 1
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(1);
        
        var user = result.Items.First();
        user.Id.Should().NotBeNullOrEmpty();
        user.Username.Should().NotBeNullOrEmpty();
        user.FullName.Should().NotBeNullOrEmpty();
        user.Email.Should().NotBeNullOrEmpty();
        user.Role.Should().NotBe(default(UserRole));
        user.FactoryName.Should().NotBeNullOrEmpty();
    }

    [Fact]
    public async Task Handle_WithEmptyDatabase_ShouldReturnEmptyResult()
    {
        // Arrange - Don't seed data
        var query = new GetUsersQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().BeEmpty();
        result.TotalCount.Should().Be(0);
        result.TotalPages.Should().Be(0);
    }
}
