using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Users.Commands.UpdateUser;

/// <summary>
/// Command to update an existing user
/// </summary>
public class UpdateUserCommand : BaseCommand
{
    public string Id { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public int? FactoryId { get; set; }
    public bool IsActive { get; set; }
    public int RecordVersion { get; set; }
}
