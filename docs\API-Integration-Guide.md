# HWS Audit Platform - API Integration Guide

## Table of Contents
1. [Overview](#overview)
2. [Layer Integration](#layer-integration)
3. [API Controller Examples](#api-controller-examples)
4. [Request/Response Flow](#requestresponse-flow)
5. [Error Handling](#error-handling)
6. [Authentication & Authorization](#authentication--authorization)
7. [File Upload/Download](#file-uploaddownload)
8. [Offline Synchronization](#offline-synchronization)
9. [Performance Considerations](#performance-considerations)
10. [Testing Examples](#testing-examples)

## Overview

This guide demonstrates how the Application and Infrastructure layers work together to provide a complete API solution for the HWS Audit Platform. The layers are integrated through dependency injection and the MediatR pattern.

### Architecture Flow
```
HTTP Request → Controller → MediatR → Application Handler → Infrastructure Service → Database/External System
```

## Layer Integration

### Startup Configuration

**Program.cs**
```csharp
var builder = WebApplication.CreateBuilder(args);

// Add layers in dependency order
builder.Services.AddDomain();           // Domain layer (if needed)
builder.Services.AddApplication();      // Application layer
builder.Services.AddInfrastructure(builder.Configuration); // Infrastructure layer

// Add API services
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add health checks
builder.Services.AddHealthChecks()
    .AddDbContext<ApplicationDbContext>()
    .AddCheck<ActiveDirectoryHealthCheck>("activedirectory")
    .AddCheck<FileStorageHealthCheck>("filestorage");

var app = builder.Build();

// Configure pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.MapHealthChecks("/health");

app.Run();
```

### Dependency Injection Flow
```
Infrastructure Layer Services
├── ApplicationDbContext (implements IApplicationDbContext)
├── Repository<T,K> (implements IRepository<T,K>)
├── UnitOfWork (implements IUnitOfWork)
├── CurrentUserService (implements ICurrentUserService)
├── FileStorageService (implements IFileStorageService)
└── ActiveDirectoryService (implements IActiveDirectoryService)

Application Layer Services
├── MediatR Handlers
├── FluentValidation Validators
├── AutoMapper Profiles
└── Pipeline Behaviors
```

## API Controller Examples

### User Management Controller

**UsersController.cs**
```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UsersController : ControllerBase
{
    private readonly IMediator _mediator;

    public UsersController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Get paginated list of users
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PaginatedResult<UserSummaryDto>>> GetUsers(
        [FromQuery] GetUsersQuery query,
        CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(query, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<UserDto>> GetUser(string id, CancellationToken cancellationToken)
    {
        var query = new GetUserQuery(id);
        var result = await _mediator.Send(query, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Create new user
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<string>> CreateUser(
        CreateUserCommand command,
        CancellationToken cancellationToken)
    {
        var userId = await _mediator.Send(command, cancellationToken);
        return CreatedAtAction(nameof(GetUser), new { id = userId }, userId);
    }

    /// <summary>
    /// Update existing user
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult> UpdateUser(
        string id,
        UpdateUserCommand command,
        CancellationToken cancellationToken)
    {
        if (id != command.Id)
            return BadRequest("ID mismatch");

        await _mediator.Send(command, cancellationToken);
        return NoContent();
    }
}
```

### Audit Management Controller

**AuditsController.cs**
```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AuditsController : ControllerBase
{
    private readonly IMediator _mediator;

    public AuditsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Get audits assigned to current user
    /// </summary>
    [HttpGet("my-audits")]
    public async Task<ActionResult<PaginatedResult<AuditSummaryDto>>> GetMyAudits(
        [FromQuery] GetMyAuditsQuery query,
        CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(query, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Create new audit
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin,Manager")]
    public async Task<ActionResult<string>> CreateAudit(
        CreateAuditCommand command,
        CancellationToken cancellationToken)
    {
        var auditId = await _mediator.Send(command, cancellationToken);
        return CreatedAtAction(nameof(GetAudit), new { id = auditId }, auditId);
    }

    /// <summary>
    /// Start audit execution
    /// </summary>
    [HttpPost("{id}/start")]
    public async Task<ActionResult> StartAudit(string id, CancellationToken cancellationToken)
    {
        var command = new StartAuditCommand { AuditId = id };
        await _mediator.Send(command, cancellationToken);
        return NoContent();
    }

    /// <summary>
    /// Submit audit answer
    /// </summary>
    [HttpPost("{auditId}/answers")]
    public async Task<ActionResult> SubmitAnswer(
        string auditId,
        SubmitAuditAnswerCommand command,
        CancellationToken cancellationToken)
    {
        command.AuditId = auditId;
        await _mediator.Send(command, cancellationToken);
        return NoContent();
    }
}
```

### File Management Controller

**FilesController.cs**
```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class FilesController : ControllerBase
{
    private readonly IFileStorageService _fileStorageService;

    public FilesController(IFileStorageService fileStorageService)
    {
        _fileStorageService = fileStorageService;
    }

    /// <summary>
    /// Upload audit attachment
    /// </summary>
    [HttpPost("upload")]
    public async Task<ActionResult<FileUploadResult>> UploadFile(
        IFormFile file,
        CancellationToken cancellationToken)
    {
        if (file == null || file.Length == 0)
            return BadRequest("No file provided");

        using var stream = file.OpenReadStream();
        var filePath = await _fileStorageService.UploadFileAsync(
            file.FileName,
            file.ContentType,
            stream,
            cancellationToken);

        var result = new FileUploadResult
        {
            FilePath = filePath,
            FileName = file.FileName,
            ContentType = file.ContentType,
            Size = file.Length
        };

        return Ok(result);
    }

    /// <summary>
    /// Download file
    /// </summary>
    [HttpGet("download/{*filePath}")]
    public async Task<ActionResult> DownloadFile(string filePath, CancellationToken cancellationToken)
    {
        if (!await _fileStorageService.FileExistsAsync(filePath, cancellationToken))
            return NotFound();

        var stream = await _fileStorageService.DownloadFileAsync(filePath, cancellationToken);
        var fileName = Path.GetFileName(filePath);

        return File(stream, "application/octet-stream", fileName);
    }

    /// <summary>
    /// Generate temporary download URL
    /// </summary>
    [HttpGet("download-url/{*filePath}")]
    public async Task<ActionResult<string>> GetDownloadUrl(string filePath, CancellationToken cancellationToken)
    {
        if (!await _fileStorageService.FileExistsAsync(filePath, cancellationToken))
            return NotFound();

        var url = await _fileStorageService.GenerateDownloadUrlAsync(
            filePath,
            TimeSpan.FromHours(1),
            cancellationToken);

        return Ok(url);
    }
}
```

## Request/Response Flow

### Successful Request Flow
```
1. HTTP Request arrives at Controller
2. Controller calls MediatR.Send(command/query)
3. MediatR Pipeline Behaviors execute:
   - ValidationBehavior validates input
   - LoggingBehavior logs request start
4. Application Handler executes:
   - Uses Infrastructure services (Repository, FileStorage, etc.)
   - Applies business logic
   - Saves changes through UnitOfWork
5. DomainEventBehavior publishes domain events
6. LoggingBehavior logs completion
7. Controller returns HTTP response
```

### Example: Creating a User
```csharp
// 1. HTTP POST /api/users
{
  "username": "john.doe",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "role": "Auditor",
  "factoryId": 1
}

// 2. Controller → MediatR
var userId = await _mediator.Send(command, cancellationToken);

// 3. ValidationBehavior validates:
// - Username is unique
// - Email is valid and unique
// - Factory exists

// 4. CreateUserCommandHandler executes:
// - Creates User domain entity
// - Saves to database via Repository
// - Returns user ID

// 5. HTTP Response
{
  "userId": "01HQXYZ123456789ABCDEF"
}
```

## Error Handling

### Global Exception Handler

**GlobalExceptionMiddleware.cs**
```csharp
public class GlobalExceptionMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = context.Response;
        response.ContentType = "application/json";

        var errorResponse = exception switch
        {
            ValidationException ex => new ErrorResponse
            {
                StatusCode = 400,
                Message = "Validation failed",
                Errors = ex.Errors
            },
            NotFoundException ex => new ErrorResponse
            {
                StatusCode = 404,
                Message = ex.Message
            },
            ForbiddenException ex => new ErrorResponse
            {
                StatusCode = 403,
                Message = ex.Message
            },
            ConflictException ex => new ErrorResponse
            {
                StatusCode = 409,
                Message = ex.Message
            },
            _ => new ErrorResponse
            {
                StatusCode = 500,
                Message = "An internal server error occurred"
            }
        };

        response.StatusCode = errorResponse.StatusCode;
        await response.WriteAsync(JsonSerializer.Serialize(errorResponse));
    }
}
```

### Error Response Models
```csharp
public class ErrorResponse
{
    public int StatusCode { get; set; }
    public string Message { get; set; } = string.Empty;
    public IDictionary<string, string[]>? Errors { get; set; }
    public string? TraceId { get; set; }
}
```

## Authentication & Authorization

### JWT Configuration
```csharp
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
        };
    });

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));
    options.AddPolicy("ManagerOrAdmin", policy => policy.RequireRole("Manager", "Admin"));
});
```

### Claims Setup
```csharp
var claims = new[]
{
    new Claim(ClaimTypes.NameIdentifier, user.Id),
    new Claim(ClaimTypes.Name, user.Username),
    new Claim(ClaimTypes.Email, user.Email),
    new Claim(ClaimTypes.Role, user.Role.ToString()),
    new Claim("FactoryId", user.FactoryId?.ToString() ?? "")
};
```

## File Upload/Download

### Upload with Validation
```csharp
[HttpPost("upload")]
public async Task<ActionResult<FileUploadResult>> UploadFile(IFormFile file)
{
    // Validation
    if (file == null || file.Length == 0)
        return BadRequest("No file provided");

    if (file.Length > 10 * 1024 * 1024) // 10MB limit
        return BadRequest("File too large");

    var allowedTypes = new[] { "image/jpeg", "image/png", "application/pdf" };
    if (!allowedTypes.Contains(file.ContentType))
        return BadRequest("File type not allowed");

    // Upload
    using var stream = file.OpenReadStream();
    var filePath = await _fileStorageService.UploadFileAsync(
        file.FileName,
        file.ContentType,
        stream);

    return Ok(new FileUploadResult { FilePath = filePath });
}
```

### Secure Download
```csharp
[HttpGet("download/{*filePath}")]
public async Task<ActionResult> DownloadFile(string filePath)
{
    // Security: Validate user has access to this file
    // This would typically check against audit attachments table
    
    if (!await _fileStorageService.FileExistsAsync(filePath))
        return NotFound();

    var stream = await _fileStorageService.DownloadFileAsync(filePath);
    var fileName = Path.GetFileName(filePath);

    return File(stream, "application/octet-stream", fileName);
}
```

## Offline Synchronization

### Sync Endpoint
```csharp
[HttpPost("sync")]
public async Task<ActionResult<SyncResult>> SyncData(SyncRequest request)
{
    var command = new SyncDataCommand
    {
        LastSyncTimestamp = request.LastSyncTimestamp,
        LocalChanges = request.Changes
    };

    var result = await _mediator.Send(command);
    return Ok(result);
}
```

### Conflict Resolution
```csharp
public class SyncDataCommandHandler : BaseCommandHandler<SyncDataCommand, SyncResult>
{
    public override async Task<SyncResult> Handle(SyncDataCommand request, CancellationToken cancellationToken)
    {
        var result = new SyncResult();

        foreach (var change in request.LocalChanges)
        {
            try
            {
                // Check for conflicts using RecordVersion
                var serverEntity = await _repository.GetByIdAsync(change.Id);
                
                if (serverEntity?.RecordVersion > change.RecordVersion)
                {
                    // Conflict detected
                    result.Conflicts.Add(new SyncConflict
                    {
                        EntityId = change.Id,
                        LocalVersion = change.RecordVersion,
                        ServerVersion = serverEntity.RecordVersion,
                        ServerData = _mapper.Map<EntityDto>(serverEntity)
                    });
                }
                else
                {
                    // Apply change
                    await ApplyChange(change);
                    result.AppliedChanges.Add(change.Id);
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add(new SyncError
                {
                    EntityId = change.Id,
                    Message = ex.Message
                });
            }
        }

        return result;
    }
}
```

## Performance Considerations

### Caching Strategy
```csharp
// Add memory caching
builder.Services.AddMemoryCache();

// Cache frequently accessed data
[HttpGet("templates")]
public async Task<ActionResult<List<AuditTemplateDto>>> GetTemplates()
{
    var cacheKey = "audit-templates";
    
    if (!_cache.TryGetValue(cacheKey, out List<AuditTemplateDto> templates))
    {
        var query = new GetAuditTemplatesQuery();
        templates = await _mediator.Send(query);
        
        _cache.Set(cacheKey, templates, TimeSpan.FromMinutes(30));
    }

    return Ok(templates);
}
```

### Pagination Best Practices
```csharp
[HttpGet]
public async Task<ActionResult<PaginatedResult<AuditSummaryDto>>> GetAudits(
    [FromQuery] int pageNumber = 1,
    [FromQuery] int pageSize = 20) // Limit page size
{
    if (pageSize > 100) pageSize = 100; // Maximum page size
    
    var query = new GetAuditsQuery
    {
        PageNumber = pageNumber,
        PageSize = pageSize
    };

    var result = await _mediator.Send(query);
    return Ok(result);
}
```

## Testing Examples

### Integration Test
```csharp
public class UsersControllerTests : IClassFixture<WebApplicationFactory<Program>>
{
    [Fact]
    public async Task CreateUser_WithValidData_ShouldReturnCreated()
    {
        // Arrange
        var command = new CreateUserCommand
        {
            Username = "testuser",
            FirstName = "Test",
            LastName = "User",
            Email = "<EMAIL>",
            Role = UserRole.Auditor
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/users", command);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Created);
        var userId = await response.Content.ReadAsStringAsync();
        userId.Should().NotBeNullOrEmpty();
    }
}
```

### Unit Test for Handler
```csharp
public class CreateUserCommandHandlerTests
{
    [Fact]
    public async Task Handle_WithValidCommand_ShouldCreateUser()
    {
        // Arrange
        var mockContext = new Mock<IApplicationDbContext>();
        var mockCurrentUser = new Mock<ICurrentUserService>();
        var handler = new CreateUserCommandHandler(mockContext.Object, mockCurrentUser.Object);

        var command = new CreateUserCommand { ... };

        // Act
        var result = await handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNullOrEmpty();
        mockContext.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }
}
```

This guide demonstrates how the Application and Infrastructure layers work together to provide a complete, production-ready API for the HWS Audit Platform.
