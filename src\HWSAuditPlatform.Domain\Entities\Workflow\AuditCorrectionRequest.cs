using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Workflow;

/// <summary>
/// Represents a request to correct a submitted audit.
/// Maps to the AuditCorrectionRequests table in the database.
/// </summary>
public class AuditCorrectionRequest : AuditableEntity<string>
{
    /// <summary>
    /// The audit being requested for correction (CUID FK). 
    /// Unique constraint ensures only one open request per audit at a time.
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string AuditId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the audit
    /// </summary>
    public virtual Audit Audit { get; set; } = null!;

    /// <summary>
    /// Auditor who submitted the request for correction (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string RequestedByUserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the requesting user
    /// </summary>
    public virtual User RequestedByUser { get; set; } = null!;

    /// <summary>
    /// Justification provided by the auditor for needing to correct the audit
    /// </summary>
    [Required]
    public string RequestReason { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp when the correction request was submitted
    /// </summary>
    public DateTime RequestedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Current status of the correction request
    /// </summary>
    public CorrectionRequestStatus Status { get; set; }

    /// <summary>
    /// Manager who reviewed the correction request (CUID FK)
    /// </summary>
    [MaxLength(25)]
    public string? ReviewedByUserId { get; set; }

    /// <summary>
    /// Navigation property for the reviewing manager
    /// </summary>
    public virtual User? ReviewedByUser { get; set; }

    /// <summary>
    /// Timestamp when the manager's review was completed
    /// </summary>
    public DateTime? ReviewedAt { get; set; }

    /// <summary>
    /// Comments provided by the manager regarding the correction request
    /// </summary>
    public string? ManagerComments { get; set; }

    /// <summary>
    /// Indicates if the request is pending approval
    /// </summary>
    public bool IsPending => Status == CorrectionRequestStatus.PendingApproval;

    /// <summary>
    /// Indicates if the request has been approved
    /// </summary>
    public bool IsApproved => Status == CorrectionRequestStatus.Approved;

    /// <summary>
    /// Indicates if the request has been denied
    /// </summary>
    public bool IsDenied => Status == CorrectionRequestStatus.Denied;

    /// <summary>
    /// Indicates if changes have been submitted
    /// </summary>
    public bool ChangesSubmitted => Status == CorrectionRequestStatus.ChangesSubmitted;

    /// <summary>
    /// Indicates if the request has been reviewed
    /// </summary>
    public bool IsReviewed => ReviewedAt.HasValue && !string.IsNullOrEmpty(ReviewedByUserId);
}
