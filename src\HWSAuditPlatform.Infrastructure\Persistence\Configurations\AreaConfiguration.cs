using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Organization;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for Area entity
/// </summary>
public class AreaConfiguration : IEntityTypeConfiguration<Area>
{
    public void Configure(EntityTypeBuilder<Area> builder)
    {
        builder.ToTable("Areas");

        // Primary Key
        builder.HasKey(a => a.Id);
        builder.Property(a => a.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(a => a.AreaName)
            .HasMaxLength(150)
            .IsRequired();

        builder.Property(a => a.Description)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(a => a.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Auditable properties
        builder.Property(a => a.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(a => a.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(a => a.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        builder.Property(a => a.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(a => new { a.FactoryId, a.AreaName })
            .IsUnique()
            .HasDatabaseName("IX_Areas_Factory_AreaName");

        builder.HasIndex(a => a.FactoryId)
            .HasDatabaseName("IX_Areas_FactoryId");

        // Relationships
        builder.HasOne(a => a.Factory)
            .WithMany(f => f.Areas)
            .HasForeignKey(a => a.FactoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.CreatedByUser)
            .WithMany()
            .HasForeignKey(a => a.CreatedByUserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        builder.HasOne(a => a.UpdatedByUser)
            .WithMany()
            .HasForeignKey(a => a.UpdatedByUserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        // Ignore domain events
        builder.Ignore(a => a.DomainEvents);
    }
}

/// <summary>
/// Entity Framework configuration for SubArea entity
/// </summary>
public class SubAreaConfiguration : IEntityTypeConfiguration<SubArea>
{
    public void Configure(EntityTypeBuilder<SubArea> builder)
    {
        builder.ToTable("SubAreas");

        // Primary Key
        builder.HasKey(sa => sa.Id);
        builder.Property(sa => sa.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(sa => sa.SubAreaName)
            .HasMaxLength(150)
            .IsRequired();

        builder.Property(sa => sa.Description)
            .HasMaxLength(500)
            .IsRequired(false);

        builder.Property(sa => sa.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Auditable properties
        builder.Property(sa => sa.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(sa => sa.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(sa => sa.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        builder.Property(sa => sa.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(sa => new { sa.AreaId, sa.SubAreaName })
            .IsUnique()
            .HasDatabaseName("IX_SubAreas_Area_SubAreaName");

        builder.HasIndex(sa => sa.AreaId)
            .HasDatabaseName("IX_SubAreas_AreaId");

        // Relationships
        builder.HasOne(sa => sa.Area)
            .WithMany(a => a.SubAreas)
            .HasForeignKey(sa => sa.AreaId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(sa => sa.CreatedByUser)
            .WithMany()
            .HasForeignKey(sa => sa.CreatedByUserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        builder.HasOne(sa => sa.UpdatedByUser)
            .WithMany()
            .HasForeignKey(sa => sa.UpdatedByUserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        // Ignore domain events
        builder.Ignore(sa => sa.DomainEvents);
    }
}
