using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for User entity
/// </summary>
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("Users");

        // Primary Key
        builder.HasKey(u => u.Id);
        builder.Property(u => u.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(u => u.Username)
            .HasMaxLength(256)
            .IsRequired();

        builder.Property(u => u.FirstName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(u => u.LastName)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(u => u.Email)
            .HasMaxLength(256)
            .IsRequired();

        builder.Property(u => u.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(u => u.LastLoginDate)
            .IsRequired(false);

        builder.Property(u => u.AdObjectGuid)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(u => u.AdDistinguishedName)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(u => u.AdSyncLastDate)
            .IsRequired(false);

        // Auditable properties
        builder.Property(u => u.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(u => u.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(u => u.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(u => u.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        builder.Property(u => u.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(u => u.Username)
            .IsUnique()
            .HasDatabaseName("IX_Users_Username");

        builder.HasIndex(u => u.Email)
            .IsUnique()
            .HasDatabaseName("IX_Users_Email");

        builder.HasIndex(u => u.AdObjectGuid)
            .IsUnique()
            .HasDatabaseName("IX_Users_AdObjectGuid")
            .HasFilter("[AdObjectGuid] IS NOT NULL");

        builder.HasIndex(u => u.RoleId)
            .HasDatabaseName("IX_Users_RoleId");

        builder.HasIndex(u => u.FactoryId)
            .HasDatabaseName("IX_Users_FactoryId");

        // Relationships
        builder.HasOne(u => u.Role)
            .WithMany()
            .HasForeignKey(u => u.RoleId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(u => u.Factory)
            .WithMany()
            .HasForeignKey(u => u.FactoryId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        // Ignore domain events (they are not persisted)
        builder.Ignore(u => u.DomainEvents);
    }
}
