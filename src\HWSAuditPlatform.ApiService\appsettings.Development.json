{"Logging": {"LogLevel": {"Default": "Debug", "System": "Information", "Microsoft": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "HWSAuditPlatform": "Debug"}}, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Jwt": {"Key": "DevelopmentKeyThatIsAtLeast32CharactersLongForTesting!", "Issuer": "HWSAuditPlatform-Dev", "Audience": "HWSAuditPlatformUsers-Dev"}, "FileStorage": {"Type": "Local"}, "LocalFileStorage": {"StoragePath": "wwwroot/uploads", "BaseUrl": "https://localhost:5001"}, "ActiveDirectory": {"Domain": "dev.local", "Username": "<EMAIL>", "Password": "dev-password", "SearchBase": "OU=Users,DC=dev,DC=local", "UseSSL": false, "Port": 389, "TimeoutSeconds": 30}, "Cors": {"AllowedOrigins": ["https://localhost:3000", "http://localhost:3000", "https://localhost:5173", "http://localhost:5173", "https://localhost:4200", "http://localhost:4200"]}, "ApiSettings": {"MaxPageSize": 50, "DefaultPageSize": 10, "MaxFileSize": 5242880, "AllowedFileTypes": ["image/jpeg", "image/png", "image/gif", "application/pdf", "text/plain"]}, "DetailedErrors": true, "EnableSensitiveDataLogging": true}