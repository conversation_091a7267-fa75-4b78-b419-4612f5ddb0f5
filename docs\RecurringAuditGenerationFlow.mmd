graph TD
    A[Scheduler WorkerTimer Trigger] --> B{Query RecurringAuditSettings};
    B -- Enabled & NextGenerationDate Due --> C[For each due setting];
    C --> D{Read AuditTemplateId & RecurrenceRules};
    C --> E{Determine Target Factory/Area/SubArea from setting};
    C --> F{Determine AssignmentType & Target Users/Groups};
    F -- Individual --> G[Create 1 Audit Instance for User];
    F -- GroupAny --> H[Create 1 Audit Instance for Group];
    F -- GroupAllScheduled --> I{Get all Users in Group};
    I --> J[For each User in Group];
    J --> K[Create 1 Audit Instance for User];
    G --> L[Set Audit Status: Scheduled];
    H --> L;
    K --> L;
    L --> M[Persist New Audit s to HWSAP_DB];
    M --> N{Calculate NextGenerationDate for Setting};
    N --> O[Update RecurringAuditSetting NextGenDate, LastGeneratedAt];
    O --> P[Persist Setting Update to HWSAP_DB];
    B -- No due settings --> Q[Idle / Wait for next trigger];