<Project Sdk="Microsoft.NET.Sdk.Worker">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-HWSAuditPlatform.SchedulerWorker-ce9eadbc-cc8a-45e3-a8e6-32e525bb8654</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" VersionOverride="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" VersionOverride="9.0.5" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HWSAuditPlatform.ServiceDefaults\HWSAuditPlatform.ServiceDefaults.csproj" />
  </ItemGroup>
</Project>