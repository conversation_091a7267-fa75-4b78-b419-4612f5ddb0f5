using HWSAuditPlatform.Infrastructure.Services.ActiveDirectory.Models;

namespace HWSAuditPlatform.Infrastructure.Services.ActiveDirectory;

/// <summary>
/// Interface for Active Directory operations
/// </summary>
public interface IActiveDirectoryService
{
    /// <summary>
    /// Gets all users from Active Directory
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of AD users</returns>
    Task<IEnumerable<AdUser>> GetUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a specific user from Active Directory by object GUID
    /// </summary>
    /// <param name="objectGuid">The AD object GUID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The AD user or null if not found</returns>
    Task<AdUser?> GetUserByObjectGuidAsync(string objectGuid, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a specific user from Active Directory by username
    /// </summary>
    /// <param name="username">The username (sAMAccountName)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The AD user or null if not found</returns>
    Task<AdUser?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all groups from Active Directory
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of AD groups</returns>
    Task<IEnumerable<AdGroup>> GetGroupsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a specific group from Active Directory by object GUID
    /// </summary>
    /// <param name="objectGuid">The AD object GUID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The AD group or null if not found</returns>
    Task<AdGroup?> GetGroupByObjectGuidAsync(string objectGuid, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets members of a specific group
    /// </summary>
    /// <param name="groupObjectGuid">The group's AD object GUID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of group members</returns>
    Task<IEnumerable<AdUser>> GetGroupMembersAsync(string groupObjectGuid, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets groups that a user is a member of
    /// </summary>
    /// <param name="userObjectGuid">The user's AD object GUID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of groups the user belongs to</returns>
    Task<IEnumerable<AdGroup>> GetUserGroupsAsync(string userObjectGuid, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates user credentials against Active Directory
    /// </summary>
    /// <param name="username">The username</param>
    /// <param name="password">The password</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if credentials are valid, false otherwise</returns>
    Task<bool> ValidateCredentialsAsync(string username, string password, CancellationToken cancellationToken = default);

    /// <summary>
    /// Tests the connection to Active Directory
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if connection is successful, false otherwise</returns>
    Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default);
}
