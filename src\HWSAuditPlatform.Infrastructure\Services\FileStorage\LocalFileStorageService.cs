using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.RegularExpressions;

namespace HWSAuditPlatform.Infrastructure.Services.FileStorage;

/// <summary>
/// Local file system implementation of file storage service
/// </summary>
public class LocalFileStorageService : IFileStorageService
{
    private readonly ILogger<LocalFileStorageService> _logger;
    private readonly LocalFileStorageOptions _options;

    public LocalFileStorageService(
        IOptions<LocalFileStorageOptions> options,
        ILogger<LocalFileStorageService> logger)
    {
        _options = options.Value;
        _logger = logger;

        // Ensure the storage directory exists
        if (!Directory.Exists(_options.StoragePath))
        {
            Directory.CreateDirectory(_options.StoragePath);
        }
    }

    public async Task<UploadedFile> UploadFileAsync(string fileName, string contentType, Stream fileStream, CancellationToken cancellationToken = default)
    {
        try
        {
            var storagePath = GenerateFilePath(fileName);
            var fullPath = Path.Combine(_options.StoragePath, storagePath);
            var directory = Path.GetDirectoryName(fullPath);

            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using var fileStreamOutput = new FileStream(fullPath, FileMode.Create, FileAccess.Write);
            await fileStream.CopyToAsync(fileStreamOutput, cancellationToken);

            // Get file size from the written file
            var fileInfo = new FileInfo(fullPath);
            var fileSize = fileInfo.Length;
            var url = await GenerateDownloadUrlAsync(storagePath, TimeSpan.FromDays(365), cancellationToken);

            _logger.LogInformation("Successfully uploaded file {FileName} to {StoragePath}", fileName, storagePath);

            return new UploadedFile
            {
                FileName = fileName,
                ContentType = contentType,
                Size = fileSize,
                StoragePath = storagePath,
                Url = url
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file {FileName}", fileName);
            throw;
        }
    }

    public async Task<UploadedFile> UploadFileAsync(string fileName, Stream fileStream, string contentType, CancellationToken cancellationToken = default)
    {
        return await UploadFileAsync(fileName, contentType, fileStream, cancellationToken);
    }

    public async Task<Stream> GetFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);

            if (!System.IO.File.Exists(fullPath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(fullPath, cancellationToken);
            return new MemoryStream(fileBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file {FilePath}", filePath);
            throw;
        }
    }

    public Task DeleteFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            
            if (System.IO.File.Exists(fullPath))
            {
                System.IO.File.Delete(fullPath);
                _logger.LogInformation("Successfully deleted file {FilePath}", filePath);
            }

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file {FilePath}", filePath);
            throw;
        }
    }

    public Task<bool> FileExistsAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            return Task.FromResult(System.IO.File.Exists(fullPath));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if file exists {FilePath}", filePath);
            throw;
        }
    }

    public Task<long> GetFileSizeAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            var fullPath = Path.Combine(_options.StoragePath, filePath);
            
            if (!System.IO.File.Exists(fullPath))
            {
                throw new FileNotFoundException($"File not found: {filePath}");
            }

            var fileInfo = new FileInfo(fullPath);
            return Task.FromResult(fileInfo.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file size {FilePath}", filePath);
            throw;
        }
    }

    public Task<string> GenerateDownloadUrlAsync(string filePath, TimeSpan expiryTime, CancellationToken cancellationToken = default)
    {
        // For local storage, we return the file path as the URL
        // In a real implementation, you might generate a signed URL or token
        var url = $"{_options.BaseUrl?.TrimEnd('/')}/files/{filePath}";
        return Task.FromResult(url);
    }

    private static string GenerateFilePath(string fileName)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy/MM/dd");
        var uniqueId = Guid.NewGuid().ToString("N")[..8];
        var extension = Path.GetExtension(fileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

        // Sanitize the filename for storage path - remove special characters and spaces
        var sanitizedName = SanitizeFileName(nameWithoutExtension);

        return Path.Combine(timestamp, $"{sanitizedName}_{uniqueId}{extension}");
    }

    private static string SanitizeFileName(string fileName)
    {
        // Remove or replace characters that are not suitable for file paths
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = fileName;

        // Replace spaces and common special characters
        sanitized = Regex.Replace(sanitized, @"[^\w\.-]", "_");

        // Remove multiple underscores
        sanitized = Regex.Replace(sanitized, @"_{2,}", "_");

        // Remove leading/trailing underscores
        sanitized = sanitized.Trim('_');

        return string.IsNullOrEmpty(sanitized) ? "file" : sanitized;
    }
}

/// <summary>
/// Configuration options for Local File Storage
/// </summary>
public class LocalFileStorageOptions
{
    public const string SectionName = "LocalFileStorage";

    public string StoragePath { get; set; } = "wwwroot/uploads";
    public string? BaseUrl { get; set; }
}

/// <summary>
/// Represents an uploaded file
/// </summary>
public class UploadedFile
{
    /// <summary>
    /// Original filename
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// Content type of the file
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// Storage path of the file
    /// </summary>
    public string StoragePath { get; set; } = string.Empty;

    /// <summary>
    /// URL to access the file
    /// </summary>
    public string Url { get; set; } = string.Empty;
}
