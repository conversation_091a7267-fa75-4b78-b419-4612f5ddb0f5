using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Users.DTOs;

/// <summary>
/// Data Transfer Object for User entity
/// </summary>
public class UserDto : AuditableDto<string>
{
    public string Username { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public int? FactoryId { get; set; }
    public string? FactoryName { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public string? AdObjectGuid { get; set; }
    public string? AdDistinguishedName { get; set; }
    public DateTime? AdSyncLastDate { get; set; }
    
    // Computed properties
    public string FullName => $"{FirstName} {LastName}".Trim();
    public bool IsAdSynced => !string.IsNullOrEmpty(AdObjectGuid);
}

/// <summary>
/// Simplified User DTO for lists and lookups
/// </summary>
public class UserSummaryDto
{
    public string Id { get; set; } = string.Empty;
    public string Username { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public bool IsActive { get; set; }
    public string? FactoryName { get; set; }
}
