using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using HWSAuditPlatform.Infrastructure.Services.ActiveDirectory;
using HWSAuditPlatform.ApiService.Models;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for authentication operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class AuthController : BaseController
{
    private readonly IActiveDirectoryService _activeDirectoryService;
    private readonly IConfiguration _configuration;

    public AuthController(
        IMediator mediator, 
        ILogger<AuthController> logger,
        IActiveDirectoryService activeDirectoryService,
        IConfiguration configuration) 
        : base(mediator, logger)
    {
        _activeDirectoryService = activeDirectoryService;
        _configuration = configuration;
    }

    /// <summary>
    /// Authenticate user and return JWT token
    /// </summary>
    /// <param name="request">Login request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication result with JWT token</returns>
    [HttpPost("login")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AuthenticationResult), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 401)]
    public async Task<ActionResult<AuthenticationResult>> Login(
        LoginRequest request,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
        {
            Logger.LogWarning("Login attempt with missing username or password");
            return Unauthorized(new ApiErrorResponse
            {
                StatusCode = 401,
                Title = "Authentication Failed",
                Detail = "Username and password are required"
            });
        }

        try
        {
            Logger.LogInformation("Login attempt for username: {Username}", request.Username);

            // Validate credentials against Active Directory
            var isValidCredentials = await _activeDirectoryService.ValidateCredentialsAsync(
                request.Username, 
                request.Password, 
                cancellationToken);

            if (!isValidCredentials)
            {
                Logger.LogWarning("Invalid credentials for username: {Username}", request.Username);
                return Unauthorized(new ApiErrorResponse
                {
                    StatusCode = 401,
                    Title = "Authentication Failed",
                    Detail = "Invalid username or password"
                });
            }

            // Get user information from Active Directory
            var adUser = await _activeDirectoryService.GetUserByUsernameAsync(request.Username, cancellationToken);
            if (adUser == null)
            {
                Logger.LogWarning("User not found in Active Directory: {Username}", request.Username);
                return Unauthorized(new ApiErrorResponse
                {
                    StatusCode = 401,
                    Title = "Authentication Failed",
                    Detail = "User not found"
                });
            }

            // TODO: Get user from database to get role and factory information
            // For now, we'll use a default role
            var userRole = "Auditor"; // This should come from the database
            var factoryId = "1"; // This should come from the database

            // Generate JWT token
            var token = GenerateJwtToken(adUser.ObjectGuid, adUser.Username, adUser.Email, userRole, factoryId);

            var result = new AuthenticationResult
            {
                Token = token,
                Username = adUser.Username,
                Email = adUser.Email,
                FullName = adUser.FullName,
                Role = userRole,
                FactoryId = factoryId,
                ExpiresAt = DateTime.UtcNow.AddHours(8) // Token expires in 8 hours
            };

            Logger.LogInformation("Successful login for username: {Username}", request.Username);
            return Success(result, "Login successful");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during login for username: {Username}", request.Username);
            return StatusCode(500, new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Authentication Error",
                Detail = "An error occurred during authentication"
            });
        }
    }

    /// <summary>
    /// Refresh JWT token
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>New JWT token</returns>
    [HttpPost("refresh")]
    [Authorize]
    [ProducesResponseType(typeof(AuthenticationResult), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 401)]
    public async Task<ActionResult<AuthenticationResult>> RefreshToken(CancellationToken cancellationToken)
    {
        var currentUserId = GetCurrentUserId();
        var currentUsername = GetCurrentUsername();
        var currentUserRole = GetCurrentUserRole();
        var currentFactoryId = GetCurrentUserFactoryId()?.ToString();

        if (string.IsNullOrEmpty(currentUserId) || string.IsNullOrEmpty(currentUsername))
        {
            Logger.LogWarning("Token refresh attempted without valid user context");
            return Unauthorized();
        }

        try
        {
            Logger.LogInformation("Token refresh for user: {Username}", currentUsername);

            // TODO: Get updated user information from database
            var userEmail = User?.FindFirst(ClaimTypes.Email)?.Value ?? "";

            // Generate new JWT token
            var token = GenerateJwtToken(currentUserId, currentUsername, userEmail, currentUserRole ?? "Auditor", currentFactoryId ?? "");

            var result = new AuthenticationResult
            {
                Token = token,
                Username = currentUsername,
                Email = userEmail,
                FullName = $"{User?.FindFirst(ClaimTypes.GivenName)?.Value} {User?.FindFirst(ClaimTypes.Surname)?.Value}".Trim(),
                Role = currentUserRole ?? "Auditor",
                FactoryId = currentFactoryId,
                ExpiresAt = DateTime.UtcNow.AddHours(8)
            };

            Logger.LogInformation("Token refreshed successfully for user: {Username}", currentUsername);
            return Success(result, "Token refreshed successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during token refresh for user: {Username}", currentUsername);
            throw;
        }
    }

    /// <summary>
    /// Logout user (client-side token invalidation)
    /// </summary>
    /// <returns>Success response</returns>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(200)]
    public ActionResult Logout()
    {
        var currentUsername = GetCurrentUsername();
        Logger.LogInformation("Logout for user: {Username}", currentUsername);
        
        // In a stateless JWT implementation, logout is handled client-side
        // The client should remove the token from storage
        return Success("Logout successful");
    }

    /// <summary>
    /// Get current user information
    /// </summary>
    /// <returns>Current user information</returns>
    [HttpGet("me")]
    [Authorize]
    [ProducesResponseType(typeof(CurrentUserInfo), 200)]
    public ActionResult<CurrentUserInfo> GetCurrentUser()
    {
        var userInfo = new CurrentUserInfo
        {
            UserId = GetCurrentUserId() ?? "",
            Username = GetCurrentUsername() ?? "",
            Email = User?.FindFirst(ClaimTypes.Email)?.Value ?? "",
            FullName = $"{User?.FindFirst(ClaimTypes.GivenName)?.Value} {User?.FindFirst(ClaimTypes.Surname)?.Value}".Trim(),
            Role = GetCurrentUserRole() ?? "",
            FactoryId = GetCurrentUserFactoryId()?.ToString()
        };

        return Success(userInfo);
    }

    private string GenerateJwtToken(string userId, string username, string email, string role, string factoryId)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key not configured")));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, userId),
            new Claim(ClaimTypes.Name, username),
            new Claim(ClaimTypes.Email, email),
            new Claim(ClaimTypes.Role, role),
            new Claim("FactoryId", factoryId),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Audience"],
            claims: claims,
            expires: DateTime.UtcNow.AddHours(8),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}

/// <summary>
/// Login request model
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password
    /// </summary>
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Authentication result model
/// </summary>
public class AuthenticationResult
{
    /// <summary>
    /// JWT token
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// User role
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// Factory ID
    /// </summary>
    public string? FactoryId { get; set; }

    /// <summary>
    /// Token expiration time
    /// </summary>
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// Current user information model
/// </summary>
public class CurrentUserInfo
{
    /// <summary>
    /// User ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// User role
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// Factory ID
    /// </summary>
    public string? FactoryId { get; set; }
}
