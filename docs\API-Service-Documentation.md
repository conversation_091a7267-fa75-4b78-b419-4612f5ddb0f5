# HWS Audit Platform - API Service Documentation

## Overview

The HWS Audit Platform API Service is a comprehensive RESTful API built with ASP.NET Core 9.0 that provides enterprise-grade functionality for manufacturing quality audit management. It implements clean architecture principles with CQRS pattern, JWT authentication, and robust security features.

## Architecture

### Clean Architecture Implementation

```
┌─────────────────────────────────────┐
│            API Controllers          │  ← HTTP endpoints, authentication
├─────────────────────────────────────┤
│         Middleware Pipeline         │  ← Exception handling, security
├─────────────────────────────────────┤
│         Application Layer           │  ← CQRS, validation, business logic
├─────────────────────────────────────┤
│        Infrastructure Layer         │  ← Database, file storage, AD
└─────────────────────────────────────┘
```

### Key Components

- **Controllers**: RESTful API endpoints with proper HTTP semantics
- **Middleware**: Global exception handling, security headers, logging
- **Authentication**: JWT-based with Active Directory integration
- **Authorization**: Role-based access control with policies
- **Validation**: Input validation with detailed error responses
- **Documentation**: Swagger/OpenAPI with comprehensive examples

## Features Implemented

### 🔐 **Authentication & Authorization**

- **JWT Authentication**: Stateless token-based authentication
- **Active Directory Integration**: Enterprise user authentication
- **Role-Based Access Control**: Admin, Manager, Auditor roles
- **Claims-Based Authorization**: Fine-grained permission control
- **Token Refresh**: Seamless token renewal without re-authentication

### 📊 **API Endpoints**

#### **Authentication Endpoints**

```
POST   /api/v1/auth/login          # Authenticate user with AD credentials
POST   /api/v1/auth/refresh        # Refresh JWT token
POST   /api/v1/auth/logout         # Logout (client-side token invalidation)
GET    /api/v1/auth/me             # Get current user information
```

#### **User Management Endpoints**

```
GET    /api/v1/users               # Get paginated users (Manager/Admin)
GET    /api/v1/users/{id}          # Get user by ID (own profile or Manager/Admin)
GET    /api/v1/users/me            # Get current user profile
GET    /api/v1/users/search        # Search users by term
POST   /api/v1/users               # Create new user (Manager/Admin)
PUT    /api/v1/users/{id}          # Update user (Manager/Admin)
```

#### **Audit Management Endpoints**

```
GET    /api/v1/audits              # Get paginated audits with filtering
GET    /api/v1/audits/{id}         # Get audit details
GET    /api/v1/audits/my-audits    # Get current user's assigned audits
POST   /api/v1/audits              # Create new audit (Manager/Admin)
POST   /api/v1/audits/{id}/start   # Start audit execution
POST   /api/v1/audits/{id}/submit  # Submit audit for review
POST   /api/v1/audits/{id}/review  # Review and approve audit (Manager/Admin)
```

#### **Organization Structure Endpoints**

```
GET    /api/v1/organization/locations                    # Get all locations
GET    /api/v1/organization/locations/{id}/factories     # Get factories by location
GET    /api/v1/organization/factories                    # Get factories with filtering
GET    /api/v1/organization/factories/{id}/areas         # Get areas by factory
GET    /api/v1/organization/areas/{id}/subareas          # Get sub-areas by area
GET    /api/v1/organization/hierarchy                    # Get complete hierarchy
POST   /api/v1/organization/factories                    # Create factory (Admin)
POST   /api/v1/organization/factories/{id}/areas         # Create area (Manager/Admin)
POST   /api/v1/organization/areas/{id}/subareas          # Create sub-area (Manager/Admin)
```

#### **File Management Endpoints**

```
POST   /api/v1/files/upload                # Upload file with validation
GET    /api/v1/files/download/{*filePath}  # Download file securely
GET    /api/v1/files/download-url/{*filePath} # Get temporary download URL
DELETE /api/v1/files/{*filePath}           # Delete file (Manager/Admin)
```

#### **Health & Monitoring Endpoints**

```
GET    /health                     # Comprehensive health check
GET    /api/v1/health              # Basic health status
GET    /api/v1/health/info         # API information
GET    /api/v1/health/ping         # Connectivity test
GET    /api/v1/health/auth-test    # Authentication test
```

### 🛡️ **Security Features**

#### **Security Middleware**

- **Global Exception Handling**: Consistent error responses
- **Security Headers**: HSTS, CSP, X-Frame-Options, etc.
- **CORS Configuration**: Configurable allowed origins
- **Request Validation**: Input sanitization and validation

#### **File Security**

- **File Type Validation**: Whitelist of allowed MIME types
- **File Size Limits**: Configurable maximum file sizes
- **Extension Validation**: Ensures extension matches content type
- **Secure Storage**: Organized file storage with unique naming

#### **Authorization Policies**

```csharp
"AdminOnly"        # Requires Admin role
"ManagerOrAdmin"   # Requires Manager or Admin role
"AuditorOrAbove"   # Requires Auditor, Manager, or Admin role
```

### 📈 **Enterprise Features**

#### **API Versioning**

- URL segment versioning (`/api/v1/`)
- Header versioning (`X-Version: 1.0`)
- Query parameter versioning (`?version=1.0`)

#### **Comprehensive Logging**

- **Structured Logging**: Serilog with JSON formatting
- **Request/Response Logging**: Automatic HTTP request logging
- **Performance Monitoring**: Request duration tracking
- **Error Tracking**: Detailed exception logging

#### **Health Monitoring**

- **Database Health**: Entity Framework connectivity
- **Active Directory Health**: LDAP connection testing
- **File Storage Health**: Storage accessibility verification
- **Custom Health Checks**: Extensible health check framework

#### **Documentation**

- **Swagger/OpenAPI**: Interactive API documentation
- **XML Comments**: Detailed endpoint documentation
- **Example Requests/Responses**: Comprehensive examples
- **Authentication Integration**: JWT token testing in Swagger

## Configuration

### Development Configuration

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=HWSAuditPlatformDb_Dev;Trusted_Connection=true"
  },
  "Jwt": {
    "Key": "DevelopmentKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "HWSAuditPlatform-Dev",
    "Audience": "HWSAuditPlatformUsers-Dev"
  },
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "wwwroot/uploads",
    "BaseUrl": "https://localhost:5001"
  },
  "Cors": {
    "AllowedOrigins": [
      "https://localhost:3000",
      "https://localhost:5173"
    ]
  }
}
```

### Production Configuration

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=prod-server;Database=HWSAuditPlatformDb;User Id=app-user;Password=***"
  },
  "FileStorage": {
    "Type": "Local"
  },
  "LocalFileStorage": {
    "StoragePath": "/app/uploads",
    "BaseUrl": "https://your-domain.com"
  },
  "ActiveDirectory": {
    "Domain": "company.local",
    "Username": "<EMAIL>",
    "Password": "***",
    "SearchBase": "OU=Users,DC=company,DC=local"
  }
}
```

## Error Handling

### Consistent Error Response Format

```json
{
  "statusCode": 400,
  "title": "Validation Error",
  "detail": "One or more validation errors occurred",
  "errors": {
    "Username": ["Username is required"],
    "Email": ["Email must be a valid email address"]
  },
  "traceId": "0HMVB9LBQK7QH:00000001",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### HTTP Status Code Usage

- **200 OK**: Successful GET requests
- **201 Created**: Successful resource creation
- **204 No Content**: Successful updates/deletes
- **400 Bad Request**: Validation errors, malformed requests
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Concurrency conflicts, duplicate resources
- **500 Internal Server Error**: Unexpected server errors

## Performance & Scalability

### Performance Features

- **Async/Await**: Non-blocking I/O throughout
- **Pagination**: Built-in pagination for large datasets
- **Filtering**: Efficient database queries with filtering
- **Connection Pooling**: EF Core connection pooling
- **Response Compression**: Automatic response compression

### Scalability Considerations

- **Stateless Design**: No server-side session state
- **JWT Tokens**: Stateless authentication
- **Health Checks**: Load balancer integration ready
- **Logging**: Structured logging for monitoring
- **Configuration**: Environment-based configuration

## Testing

### API Testing Examples

#### Authentication Test

```bash
# Login
curl -X POST "https://localhost:5001/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"john.doe","password":"password123"}'

# Use token
curl -X GET "https://localhost:5001/api/v1/users/me" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

#### File Upload Test

```bash
curl -X POST "https://localhost:5001/api/v1/files/upload" \
  -H "Authorization: Bearer {token}" \
  -F "file=@document.pdf"
```

#### Health Check Test

```bash
curl -X GET "https://localhost:5001/health"
```

### Integration Testing

- **WebApplicationFactory**: In-memory testing
- **Test Database**: Isolated test database
- **Authentication Testing**: JWT token generation for tests
- **File Upload Testing**: Multipart form data testing

## Deployment

### Production Deployment

- **Configuration**: Environment-based configuration management
- **SSL Certificates**: HTTPS with custom domains
- **Scaling**: Horizontal scaling with load balancers
- **Monitoring**: Comprehensive logging and monitoring
- **Deployment**: Blue-green deployment strategies

## Monitoring & Observability

### Logging

- **Serilog**: Structured logging with multiple sinks
- **Request Logging**: HTTP request/response logging
- **Performance Logging**: Request duration and performance metrics
- **Error Logging**: Detailed exception information

### Health Checks

- **Database**: EF Core database connectivity
- **Active Directory**: LDAP connection health
- **File Storage**: Storage service accessibility
- **Custom Checks**: Extensible health check framework

### Metrics (Ready for Implementation)

- **Request Metrics**: Request count, duration, status codes
- **Business Metrics**: User activity, audit completion rates
- **System Metrics**: Memory usage, CPU utilization
- **Custom Metrics**: Application-specific KPIs

## Security Best Practices

### Implemented Security Measures

- **HTTPS Enforcement**: Automatic HTTPS redirection
- **Security Headers**: Comprehensive security header set
- **Input Validation**: Server-side validation for all inputs
- **SQL Injection Prevention**: Parameterized queries via EF Core
- **XSS Prevention**: Output encoding and CSP headers
- **CSRF Protection**: SameSite cookies and CORS configuration

### Authentication Security

- **JWT Tokens**: Short-lived tokens with refresh capability
- **Strong Secrets**: Configurable JWT signing keys
- **Claims Validation**: Comprehensive token validation
- **Active Directory**: Enterprise authentication integration

This API service provides a robust, secure, and scalable foundation for the HWS Audit Platform with enterprise-grade features and comprehensive documentation.
