# Database Mapping Documentation

This document describes how the domain entities map to the database schema defined in `docs/database.dbml`.

## Overview

The domain entities are designed to map directly to the AuditFlowDB database schema while maintaining clean separation between domain logic and persistence concerns. The mapping follows these principles:

- **Direct Mapping**: Domain entities map one-to-one with database tables
- **Type Safety**: Enums map to database enum types
- **Concurrency Control**: RecordVersion fields support optimistic locking
- **Audit Tracking**: Created/Updated fields track entity lifecycle
- **Offline Support**: CUID primary keys enable client-side generation

## Primary Key Strategies

### CUID Primary Keys (varchar(25))
Used for entities frequently created or modified client-side (PWA offline scenarios):

| Domain Entity | Database Table | Primary Key | Reason |
|---------------|----------------|-------------|---------|
| User | Users | UserId | Client registration, offline sync |
| UserGroup | UserGroups | UserGroupId | Client group creation |
| Audit | Audits | AuditId | Offline audit creation |
| AuditAnswer | AuditAnswers | AuditAnswerId | Offline answer entry |
| AuditAttachment | AuditAttachments | AuditAttachmentId | Offline evidence upload |
| Finding | Findings | FindingId | Client finding creation |
| CorrectiveAction | CorrectiveActions | CorrectiveActionId | Client action creation |
| RecurringAuditSetting | RecurringAuditSettings | RecurringAuditSettingId | Client schedule creation |
| AuditCorrectionRequest | AuditCorrectionRequests | AuditCorrectionRequestId | Client request creation |
| AuditAnswerFailureReason | AuditAnswerFailureReasons | AuditAnswerFailureReasonId | Client failure tracking |

### Integer Primary Keys
Used for server-managed entities with stable identities:

| Domain Entity | Database Table | Primary Key | Reason |
|---------------|----------------|-------------|---------|
| Role | Roles | RoleId | Server-managed, stable |
| Location | Location | LocationId | Server-managed hierarchy |
| Factory | Factories | FactoryId | Server-managed hierarchy |
| Area | Areas | AreaId | Server-managed hierarchy |
| SubArea | SubAreas | SubAreaId | Server-managed hierarchy |
| AuditTemplate | AuditTemplates | AuditTemplateId | Server-managed templates |
| QuestionGroup | QuestionGroups | QuestionGroupId | Server-managed structure |
| Question | Questions | QuestionId | Server-managed structure |
| QuestionOption | QuestionOptions | QuestionOptionId | Server-managed options |
| AdGroupRoleMapping | AdGroupRoleMapping | AdGroupRoleMappingId | Server-managed mapping |

### Long Primary Keys
Used for high-volume, server-only entities:

| Domain Entity | Database Table | Primary Key | Reason |
|---------------|----------------|-------------|---------|
| AuditLog | AuditLogs | AuditLogId | High-volume logging |

## Enum Mappings

Domain enums map directly to database enum types:

| Domain Enum | Database Enum | Values |
|-------------|---------------|---------|
| UserRole | user_role | Admin, Manager, Auditor |
| QuestionType | question_type | YesNo, Numeric, SingleSelect, MultiSelect, ShortText, LongText, Date |
| AuditOverallStatus | audit_overall_status | Scheduled, InProgress, Submitted, PendingManagerReview, PendingCorrection, ManagerReviewed, Closed, Cancelled |
| SeverityLevel | severity_level | Critical, Major, Minor, Observation |
| FrequencyType | frequency_type | DAILY, WEEKLY, MONTHLY, YEARLY, COSTUM |
| CorrectionRequestStatus | correction_request_status | PendingApproval, Approved, Denied, ChangesSubmitted |
| AssignmentType | assignment_type | Individual, GroupAny, GroupAllScheduled |
| FindingStatus | finding_status | Open, UnderInvestigation, PendingCorrectiveAction, PendingVerification, Closed, Rejected |
| CorrectiveActionStatus | corrective_action_status | Assigned, InProgress, CompletedPendingVerification, VerifiedClosed, Cancelled, Ineffective |
| EvidenceTimingHint | evidence_timing_hint | OnSitePreferred, LaterAllowed, AnyTime |

## Field Mappings

### Common Fields

#### BaseEntity<TKey> Mappings
| Domain Property | Database Column | Type | Notes |
|-----------------|-----------------|------|-------|
| Id | [TableName]Id | varchar(25) or int | Primary key |
| CreatedAt | CreatedAt | timestamp | UTC timestamp |
| UpdatedAt | UpdatedAt | timestamp | UTC timestamp |

#### AuditableEntity<TKey> Additional Mappings
| Domain Property | Database Column | Type | Notes |
|-----------------|-----------------|------|-------|
| RecordVersion | RecordVersion | integer | Optimistic concurrency |
| CreatedByUserId | CreatedByUserId | varchar(25) | CUID FK to Users |
| UpdatedByUserId | UpdatedByUserId | varchar(25) | CUID FK to Users |

### Entity-Specific Mappings

#### User Entity
| Domain Property | Database Column | Type | Constraints |
|-----------------|-----------------|------|-------------|
| Username | Username | varchar(256) | NOT NULL, UNIQUE |
| FirstName | FirstName | varchar(100) | |
| LastName | LastName | varchar(100) | |
| Email | Email | varchar(256) | NOT NULL, UNIQUE |
| RoleId | RoleId | integer | NOT NULL, FK to Roles |
| FactoryId | FactoryId | integer | FK to Factories |
| IsActive | IsActive | boolean | NOT NULL, DEFAULT true |
| LastLoginDate | LastLoginDate | timestamp | |
| AdObjectGuid | AdObjectGuid | varchar(255) | NOT NULL (all users are AD synced) |
| AdDistinguishedName | AdDistinguishedName | text | |
| AdSyncLastDate | AdSyncLastDate | datetime | |

#### Audit Entity
| Domain Property | Database Column | Type | Constraints |
|-----------------|-----------------|------|-------------|
| AuditTemplateId | AuditTemplateId | integer | NOT NULL, FK to AuditTemplates |
| AssignmentType | AssignmentType | assignment_type | NOT NULL, DEFAULT 'Individual' |
| AssignedToUserGroupId | AssignedToUserGroupId | varchar(25) | FK to UserGroups |
| AssignedToUserId | AssignedToUserId | varchar(25) | FK to Users |
| ScheduledDate | ScheduledDate | timestamp | NOT NULL |
| DueDate | DueDate | timestamp | |
| StartedAt | StartedAt | timestamp | |
| CompletedAt | CompletedAt | timestamp | |
| OverallStatus | OverallStatus | audit_overall_status | NOT NULL |
| FactoryId | FactoryId | integer | NOT NULL, FK to Factories |
| AreaId | AreaId | integer | NOT NULL, FK to Areas |
| SubAreaId | SubAreaId | integer | FK to SubAreas |
| OverallScore | OverallScore | decimal(7,2) | |
| ManagerComments | ManagerComments | text | |
| ReviewedByUserId | ReviewedByUserId | varchar(25) | FK to Users |
| ReviewedAt | ReviewedAt | timestamp | |
| RecurringAuditSettingId | RecurringAuditSettingId | varchar(25) | FK to RecurringAuditSettings |

#### Question Entity
| Domain Property | Database Column | Type | Constraints |
|-----------------|-----------------|------|-------------|
| AuditTemplateId | AuditTemplateId | integer | NOT NULL, FK to AuditTemplates |
| QuestionGroupId | QuestionGroupId | integer | FK to QuestionGroups |
| QuestionText | QuestionText | text | NOT NULL |
| QuestionType | QuestionType | question_type | NOT NULL |
| DisplayOrder | DisplayOrder | integer | NOT NULL, DEFAULT 0 |
| IsRequired | IsRequired | boolean | NOT NULL, DEFAULT true |
| Weight | Weight | decimal(5,2) | |
| HelpText | HelpText | text | |
| ParentQuestionId | ParentQuestionId | integer | FK to Questions |
| TriggerAnswerValue | TriggerAnswerValue | varchar(255) | |
| SeverityLevel | SeverityLevel | severity_level | |
| EvidenceRequired | EvidenceRequired | boolean | NOT NULL, DEFAULT false |
| EvidenceInstructions | EvidenceInstructions | text | |
| EvidenceTimingHint | EvidenceTimingHint | evidence_timing_hint | |
| AllowedEvidenceTypes | AllowedEvidenceTypes | text | |

## Composite Keys and Junction Tables

### UserGroupMember
| Domain Property | Database Column | Type | Constraints |
|-----------------|-----------------|------|-------------|
| UserGroupId | UserGroupId | varchar(25) | NOT NULL, FK to UserGroups |
| UserId | UserId | varchar(25) | NOT NULL, FK to Users |
| | PRIMARY KEY (UserGroupId, UserId) | | |

### AuditAnswerSelectedOption
| Domain Property | Database Column | Type | Constraints |
|-----------------|-----------------|------|-------------|
| AuditAnswerId | AuditAnswerId | varchar(25) | NOT NULL, FK to AuditAnswers |
| QuestionOptionId | QuestionOptionId | integer | NOT NULL, FK to QuestionOptions |
| | PRIMARY KEY (AuditAnswerId, QuestionOptionId) | | |

## Unique Constraints

### Template and Versioning
- `(TemplateName, Version)` on AuditTemplates
- Ensures unique template versions

### Organizational Hierarchy
- `(FactoryId, AreaName)` on Areas
- `(AreaId, SubAreaName)` on SubAreas
- Ensures unique names within parent scope

### User and Group Management
- `Username` on Users
- `Email` on Users
- `GroupName` on UserGroups
- `AdGroupName` on AdGroupRoleMapping

### Audit Correction Workflow
- `AuditId` on AuditCorrectionRequests
- Ensures only one active correction request per audit

## Indexes and Performance

### Recommended Indexes
```sql
-- Audit queries by status and assignment
CREATE INDEX IX_Audits_OverallStatus ON Audits(OverallStatus);
CREATE INDEX IX_Audits_AssignedToUserId ON Audits(AssignedToUserId);
CREATE INDEX IX_Audits_AssignedToUserGroupId ON Audits(AssignedToUserGroupId);
CREATE INDEX IX_Audits_ScheduledDate ON Audits(ScheduledDate);

-- Answer queries by audit
CREATE INDEX IX_AuditAnswers_AuditId ON AuditAnswers(AuditId);
CREATE INDEX IX_AuditAnswers_QuestionId ON AuditAnswers(QuestionId);

-- Finding queries by status and severity
CREATE INDEX IX_Findings_Status ON Findings(Status);
CREATE INDEX IX_Findings_FindingSeverityLevel ON Findings(FindingSeverityLevel);
CREATE INDEX IX_Findings_DueDate ON Findings(DueDate);

-- Corrective action queries
CREATE INDEX IX_CorrectiveActions_AssignedToUserId ON CorrectiveActions(AssignedToUserId);
CREATE INDEX IX_CorrectiveActions_Status ON CorrectiveActions(Status);
CREATE INDEX IX_CorrectiveActions_DueDate ON CorrectiveActions(DueDate);

-- Audit log queries
CREATE INDEX IX_AuditLogs_EntityType_EntityId ON AuditLogs(EntityType, EntityId);
CREATE INDEX IX_AuditLogs_UserId ON AuditLogs(UserId);
CREATE INDEX IX_AuditLogs_EventTimestamp ON AuditLogs(EventTimestamp);
```

## Data Type Considerations

### Decimal Precision
- **AuditScore**: decimal(7,2) - supports scores up to 99999.99
- **QuestionWeight**: decimal(5,2) - supports weights up to 999.99
- **AnswerNumeric**: decimal(18,4) - high precision for numeric answers

### Text Fields
- **Short Text**: varchar with specific lengths for performance
- **Long Text**: text type for unlimited content
- **JSON Storage**: Consider JSON columns for complex data structures

### Date and Time
- **timestamp**: UTC timestamps for all datetime fields
- **date**: Date-only fields for due dates and completion dates
- **datetime**: Legacy compatibility where needed

## Migration Considerations

### Schema Evolution
- Use database migrations for schema changes
- Maintain backward compatibility during deployments
- Plan for data migration scripts

### Data Seeding
- Seed essential data (Roles, default Locations)
- Provide sample data for development/testing
- Consider data import utilities for production setup

### Performance Optimization
- Monitor query performance with actual data volumes
- Adjust indexes based on usage patterns
- Consider partitioning for high-volume tables (AuditLogs)

## Synchronization Support

### Offline Capability
- CUID primary keys enable offline entity creation
- RecordVersion fields support conflict detection
- Timestamp fields track client vs server timing

### Conflict Resolution
- Last-write-wins based on UpdatedAt timestamps
- RecordVersion comparison for optimistic locking
- Manual resolution for critical conflicts

### Sync Metadata
Consider additional fields for sync scenarios:
- `SyncStatus`: Track synchronization state
- `LastSyncedAt`: Track last successful sync
- `ConflictResolution`: Track how conflicts were resolved
