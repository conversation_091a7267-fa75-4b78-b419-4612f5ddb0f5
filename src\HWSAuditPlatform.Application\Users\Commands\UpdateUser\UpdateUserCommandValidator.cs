using FluentValidation;
using HWSAuditPlatform.Application.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Commands.UpdateUser;

/// <summary>
/// Validator for UpdateUserCommand
/// </summary>
public class UpdateUserCommandValidator : AbstractValidator<UpdateUserCommand>
{
    private readonly IApplicationDbContext _context;

    public UpdateUserCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(x => x.Id)
            .NotEmpty().WithMessage("User ID is required");

        RuleFor(x => x.FirstName)
            .NotEmpty().WithMessage("First name is required")
            .MaximumLength(100).WithMessage("First name must not exceed 100 characters");

        RuleFor(x => x.LastName)
            .NotEmpty().WithMessage("Last name is required")
            .MaximumLength(100).WithMessage("Last name must not exceed 100 characters");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Email must be a valid email address")
            .MaximumLength(256).WithMessage("Email must not exceed 256 characters")
            .MustAsync(BeUniqueEmail).WithMessage("Email already exists");

        RuleFor(x => x.Role)
            .IsInEnum().WithMessage("Role must be a valid user role");

        RuleFor(x => x.FactoryId)
            .MustAsync(BeValidFactory).WithMessage("Factory does not exist")
            .When(x => x.FactoryId.HasValue);

        RuleFor(x => x.RecordVersion)
            .GreaterThan(0).WithMessage("Record version must be greater than 0");
    }

    private async Task<bool> BeUniqueEmail(UpdateUserCommand command, string email, CancellationToken cancellationToken)
    {
        return !await _context.Users.AnyAsync(u => u.Email == email && u.Id != command.Id, cancellationToken);
    }

    private async Task<bool> BeValidFactory(int? factoryId, CancellationToken cancellationToken)
    {
        if (!factoryId.HasValue) return true;
        return await _context.Factories.AnyAsync(f => f.Id == factoryId.Value && f.IsActive, cancellationToken);
    }
}
