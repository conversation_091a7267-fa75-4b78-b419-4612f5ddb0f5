# HWS Audit Platform - Testing Guide

## Overview

This guide covers the comprehensive testing strategy for the HWS Audit Platform, including unit tests, integration tests, and testing best practices.

## Test Structure

```
src/HWSAuditPlatform.Tests/
├── Common/                           # Shared test utilities
│   ├── TestDbContextFactory.cs      # Database context for testing
│   ├── MockServices.cs              # Mock service factories
│   └── BaseTestClass.cs             # Base classes for tests
├── Domain/                           # Domain layer tests (existing)
├── Application/                      # Application layer tests
│   ├── Users/
│   │   ├── Commands/                 # Command handler tests
│   │   └── Queries/                  # Query handler tests
│   └── Common/
│       └── MappingTests.cs           # AutoMapper configuration tests
├── Infrastructure/                   # Infrastructure layer tests
│   ├── Persistence/                  # Database and EF tests
│   └── Services/                     # External service tests
└── Integration/                      # End-to-end API tests
    └── Controllers/                  # Controller integration tests
```

## Test Categories

### 1. Domain Tests ✅ (Already Complete)
- **Location**: `src/HWSAuditPlatform.Tests/Domain/`
- **Purpose**: Test business logic, entities, value objects, and domain rules
- **Coverage**: 95%+ required for critical business logic
- **Examples**: User creation, audit state transitions, business rule validation

### 2. Application Tests 🆕 (Newly Created)
- **Location**: `src/HWSAuditPlatform.Tests/Application/`
- **Purpose**: Test command/query handlers, DTOs, and application workflows
- **Coverage**: 85%+ required
- **Examples**: 
  - `CreateUserCommandHandlerTests.cs` - Tests user creation workflow
  - `GetUsersQueryHandlerTests.cs` - Tests user querying with filters
  - `MappingTests.cs` - Tests AutoMapper configurations

### 3. Infrastructure Tests 🆕 (Newly Created)
- **Location**: `src/HWSAuditPlatform.Tests/Infrastructure/`
- **Purpose**: Test database access, external services, and infrastructure concerns
- **Coverage**: 70%+ required
- **Examples**:
  - `ApplicationDbContextTests.cs` - Tests EF Core configurations
  - `FileStorageServiceTests.cs` - Tests file upload/download
  - `ActiveDirectoryServiceTests.cs` - Tests AD integration

### 4. Integration Tests 🆕 (Newly Created)
- **Location**: `src/HWSAuditPlatform.Tests/Integration/`
- **Purpose**: Test complete API workflows end-to-end
- **Coverage**: Key user journeys must be covered
- **Examples**:
  - `UsersControllerTests.cs` - Tests complete user management API

## Running Tests

### Quick Start

```bash
# Run all tests
dotnet test

# Run with coverage
.\run-tests.ps1 -Coverage

# Run specific category
.\run-tests.ps1 -Filter "Application"

# Run in watch mode
.\run-tests.ps1 -Watch
```

### PowerShell Test Runner

The `run-tests.ps1` script provides enhanced testing capabilities:

```powershell
# Basic usage
.\run-tests.ps1                           # Run all tests
.\run-tests.ps1 -Coverage                 # Run with coverage report
.\run-tests.ps1 -Filter "Users"           # Run only user-related tests
.\run-tests.ps1 -Watch                    # Run in watch mode
.\run-tests.ps1 -Verbose                  # Run with detailed output

# Combined options
.\run-tests.ps1 -Coverage -Filter "Application" -Verbose
```

### Test Filters

Use filters to run specific test categories:

```bash
# Run by namespace
dotnet test --filter "FullyQualifiedName~Application"
dotnet test --filter "FullyQualifiedName~Infrastructure"
dotnet test --filter "FullyQualifiedName~Integration"

# Run by class name
dotnet test --filter "ClassName~UserTests"
dotnet test --filter "ClassName~CommandHandler"

# Run by test name
dotnet test --filter "Name~CreateUser"
dotnet test --filter "Name~WithValidData"
```

## Test Utilities

### TestDbContextFactory

Creates in-memory database contexts for testing:

```csharp
// Create empty context
var context = TestDbContextFactory.CreateInMemoryContext();

// Create context with test data
var context = await TestDbContextFactory.CreateSeededContextAsync();

// Manual seeding
var context = TestDbContextFactory.CreateInMemoryContext();
await TestDbContextFactory.SeedTestDataAsync(context);
```

### MockServices

Factory for creating mock services:

```csharp
// Mock current user service
var mockCurrentUser = MockServices.CreateCurrentUserService("user-id", "username");

// Mock domain event service
var mockDomainEvents = MockServices.CreateDomainEventService();

// Mock repository
var mockUserRepo = MockServices.CreateRepository<User, string>();
```

### Base Test Classes

```csharp
// Basic test class with common mocks
public class MyTests : BaseTestClass
{
    // MockCurrentUserService, MockDomainEventService available
}

// Test class with database context
public class MyDbTests : BaseDbTestClass
{
    // Context property available, plus all base mocks
    
    protected override async Task SeedTestDataAsync()
    {
        await base.SeedTestDataAsync();
        // Add custom test data
    }
}
```

## Test Data Management

### Seeded Test Data

The `TestDbContextFactory.SeedTestDataAsync()` method creates:

- **3 Roles**: Admin, Manager, Auditor
- **1 Location**: Test Location with address
- **1 Factory**: Test Factory linked to location
- **3 Users**: admin, manager, auditor with different roles

### Custom Test Data

```csharp
// Create test users
var user = User.Create(
    username: "testuser",
    firstName: "Test",
    lastName: "User",
    email: "<EMAIL>",
    roleId: 1,
    factoryId: null,
    isActive: true,
    adObjectGuid: null,
    adDistinguishedName: null,
    createdByUserId: "creator"
);

Context.Users.Add(user);
await Context.SaveChangesAsync();
```

## Testing Patterns

### Command Handler Tests

```csharp
[Fact]
public async Task Handle_WithValidCommand_ShouldCreateEntity()
{
    // Arrange
    await SeedTestDataAsync();
    var command = new CreateEntityCommand { /* properties */ };

    // Act
    var result = await _handler.Handle(command, CancellationToken.None);

    // Assert
    result.Should().NotBeNullOrEmpty();
    var entity = await Context.Entities.FirstOrDefaultAsync(e => e.Id == result);
    entity.Should().NotBeNull();
    entity!.Property.Should().Be(command.Property);
}
```

### Query Handler Tests

```csharp
[Fact]
public async Task Handle_WithFilters_ShouldReturnFilteredResults()
{
    // Arrange
    await SeedTestDataAsync();
    var query = new GetEntitiesQuery { Filter = "value" };

    // Act
    var result = await _handler.Handle(query, CancellationToken.None);

    // Assert
    result.Should().NotBeNull();
    result.Items.Should().OnlyContain(item => item.Property.Contains("value"));
}
```

### Integration Tests

```csharp
[Fact]
public async Task PostEntity_WithValidData_ShouldReturnCreated()
{
    // Arrange
    var command = new CreateEntityCommand { /* properties */ };

    // Act
    var response = await _client.PostAsJsonAsync("/api/v1/entities", command);

    // Assert
    response.StatusCode.Should().Be(HttpStatusCode.Created);
    var entityId = await response.Content.ReadAsStringAsync();
    entityId.Should().NotBeNullOrEmpty();
}
```

## Coverage Requirements

### Minimum Coverage Targets

- **Domain Layer**: 95% (critical business logic)
- **Application Layer**: 85% (use cases and workflows)
- **Infrastructure Layer**: 70% (external integrations)
- **Overall Project**: 80%

### Coverage Reporting

```bash
# Generate coverage report
.\run-tests.ps1 -Coverage

# View report
# Opens automatically on Windows: TestResults/CoverageReport/index.html
```

### Coverage Exclusions

Exclude from coverage:
- Auto-generated code
- Program.cs and Startup.cs
- Migration files
- DTOs with no logic
- Configuration classes

## Continuous Integration

### GitHub Actions Example

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: '10.0.x'
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build
      run: dotnet build --no-restore
    
    - name: Test
      run: dotnet test --no-build --collect:"XPlat Code Coverage"
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

## Best Practices

### Test Naming

```csharp
// Pattern: MethodName_Scenario_ExpectedResult
[Fact]
public async Task CreateUser_WithValidData_ShouldReturnUserId()

[Fact]
public async Task CreateUser_WithDuplicateEmail_ShouldThrowException()

[Fact]
public async Task GetUsers_WithSearchTerm_ShouldFilterResults()
```

### Test Organization

- **One test class per handler/service**
- **Group related tests with nested classes**
- **Use descriptive test names**
- **Follow Arrange-Act-Assert pattern**

### Assertions

```csharp
// Use FluentAssertions for readable tests
result.Should().NotBeNull();
result.Items.Should().HaveCount(3);
result.Items.Should().OnlyContain(u => u.IsActive);
response.StatusCode.Should().Be(HttpStatusCode.Created);
```

### Test Data

- **Use realistic test data**
- **Avoid magic numbers and strings**
- **Create data builders for complex objects**
- **Clean up test data between tests**

### Async Testing

```csharp
// Always use async/await for async methods
[Fact]
public async Task TestMethod_ShouldWork()
{
    var result = await _service.DoSomethingAsync();
    result.Should().NotBeNull();
}
```

## Troubleshooting

### Common Issues

1. **Database conflicts**: Use unique database names per test
2. **Async deadlocks**: Always use ConfigureAwait(false) or async all the way
3. **Flaky tests**: Avoid time-dependent assertions, use deterministic data
4. **Memory leaks**: Dispose contexts and clients in test cleanup

### Debugging Tests

```bash
# Run single test with detailed output
dotnet test --filter "TestMethodName" --verbosity detailed

# Debug in Visual Studio
# Set breakpoints and use Test Explorer
```

## Next Steps

1. **Run the existing tests** to ensure everything works
2. **Add missing Application layer tests** for other entities
3. **Create Infrastructure tests** for remaining services
4. **Add Integration tests** for all controllers
5. **Set up CI/CD pipeline** with automated testing
6. **Monitor coverage** and improve low-coverage areas

---

## Quick Reference

```bash
# Essential commands
dotnet test                                    # Run all tests
.\run-tests.ps1 -Coverage                     # Run with coverage
.\run-tests.ps1 -Filter "Application"         # Run application tests
.\run-tests.ps1 -Watch                        # Watch mode
dotnet test --filter "Name~CreateUser"        # Run specific tests
```

This testing infrastructure provides a solid foundation for maintaining high code quality and catching regressions early in the development process.
