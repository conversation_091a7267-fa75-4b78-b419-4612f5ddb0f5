using System.Net;
using System.Text.Json;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.ApiService.Models;

namespace HWSAuditPlatform.ApiService.Middleware;

/// <summary>
/// Global exception handling middleware
/// </summary>
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = exception switch
        {
            ValidationException ex => new ApiErrorResponse
            {
                StatusCode = (int)HttpStatusCode.BadRequest,
                Title = "Validation Error",
                Detail = "One or more validation errors occurred",
                Errors = ex.Errors,
                TraceId = context.TraceIdentifier
            },
            NotFoundException ex => new ApiErrorResponse
            {
                StatusCode = (int)HttpStatusCode.NotFound,
                Title = "Resource Not Found",
                Detail = ex.Message,
                TraceId = context.TraceIdentifier
            },
            ForbiddenException ex => new ApiErrorResponse
            {
                StatusCode = (int)HttpStatusCode.Forbidden,
                Title = "Access Forbidden",
                Detail = ex.Message,
                TraceId = context.TraceIdentifier
            },
            ConflictException ex => new ApiErrorResponse
            {
                StatusCode = (int)HttpStatusCode.Conflict,
                Title = "Conflict",
                Detail = ex.Message,
                TraceId = context.TraceIdentifier
            },
            BusinessRuleViolationException ex => new ApiErrorResponse
            {
                StatusCode = (int)HttpStatusCode.BadRequest,
                Title = "Business Rule Violation",
                Detail = ex.Message,
                TraceId = context.TraceIdentifier
            },
            UnauthorizedAccessException ex => new ApiErrorResponse
            {
                StatusCode = (int)HttpStatusCode.Unauthorized,
                Title = "Unauthorized",
                Detail = ex.Message,
                TraceId = context.TraceIdentifier
            },
            _ => new ApiErrorResponse
            {
                StatusCode = (int)HttpStatusCode.InternalServerError,
                Title = "Internal Server Error",
                Detail = "An internal server error occurred",
                TraceId = context.TraceIdentifier
            }
        };

        context.Response.StatusCode = response.StatusCode;

        var jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        var jsonResponse = JsonSerializer.Serialize(response, jsonOptions);
        await context.Response.WriteAsync(jsonResponse);
    }
}
