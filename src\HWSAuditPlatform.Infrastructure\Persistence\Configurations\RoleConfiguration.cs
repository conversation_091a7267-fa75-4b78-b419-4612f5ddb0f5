using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for Role entity
/// </summary>
public class RoleConfiguration : IEntityTypeConfiguration<Role>
{
    public void Configure(EntityTypeBuilder<Role> builder)
    {
        builder.ToTable("Roles");

        // Primary Key
        builder.HasKey(r => r.Id);
        builder.Property(r => r.Id)
            .ValueGeneratedOnAdd();

        // Properties
        builder.Property(r => r.RoleName)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(r => r.Description)
            .HasMaxLength(255)
            .IsRequired(false);

        // Base entity properties
        builder.Property(r => r.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(r => r.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        // Indexes
        builder.HasIndex(r => r.RoleName)
            .IsUnique()
            .HasDatabaseName("IX_Roles_RoleName");

        // Ignore domain events
        builder.Ignore(r => r.DomainEvents);

        // Seed data
        builder.HasData(
            new Role { Id = 1, RoleName = UserRole.Admin, Description = "System Administrator with full access", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new Role { Id = 2, RoleName = UserRole.Manager, Description = "Manager with audit oversight and approval capabilities", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
            new Role { Id = 3, RoleName = UserRole.Auditor, Description = "Auditor who performs audit activities", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
        );
    }
}
