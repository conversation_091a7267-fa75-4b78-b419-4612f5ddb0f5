using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Entities.Scheduling;
using HWSAuditPlatform.Domain.Entities.Workflow;

namespace HWSAuditPlatform.Application.Interfaces;

/// <summary>
/// Application database context interface
/// </summary>
public interface IApplicationDbContext
{
    // User Management
    DbSet<Role> Roles { get; }
    DbSet<User> Users { get; }
    DbSet<UserGroup> UserGroups { get; }
    DbSet<UserGroupMember> UserGroupMembers { get; }
    DbSet<AdGroupRoleMapping> AdGroupRoleMappings { get; }

    // Organizational Structure
    DbSet<Location> Locations { get; }
    DbSet<Factory> Factories { get; }
    DbSet<Area> Areas { get; }
    DbSet<SubArea> SubAreas { get; }

    // Audit Templates
    DbSet<AuditTemplate> AuditTemplates { get; }
    DbSet<QuestionGroup> QuestionGroups { get; }
    DbSet<Question> Questions { get; }
    DbSet<QuestionOption> QuestionOptions { get; }

    // Audit Execution
    DbSet<Audit> Audits { get; }
    DbSet<AuditAnswer> AuditAnswers { get; }
    DbSet<AuditAnswerSelectedOption> AuditAnswerSelectedOptions { get; }
    DbSet<AuditAnswerFailureReason> AuditAnswerFailureReasons { get; }
    DbSet<AuditAttachment> AuditAttachments { get; }

    // Findings & Corrective Actions
    DbSet<Finding> Findings { get; }
    DbSet<CorrectiveAction> CorrectiveActions { get; }

    // Scheduling
    DbSet<RecurringAuditSetting> RecurringAuditSettings { get; }
    DbSet<RecurrenceRule> RecurrenceRules { get; }

    // Workflow
    DbSet<AuditCorrectionRequest> AuditCorrectionRequests { get; }
    DbSet<AuditLog> AuditLogs { get; }

    /// <summary>
    /// Saves all changes made in this context to the database
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The number of state entries written to the database</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
