var builder = DistributedApplication.CreateBuilder(args);

var apiService = builder.AddProject<Projects.HWSAuditPlatform_ApiService>("apiservice");

builder.AddProject<Projects.HWSAuditPlatform_Web>("webfrontend")
    .WithExternalHttpEndpoints()
    .WithReference(apiService)
    .WaitFor(apiService);

builder.AddProject<Projects.HWSAuditPlatform_SchedulerWorker>("hwsauditplatform-schedulerworker");

builder.Build().Run();
