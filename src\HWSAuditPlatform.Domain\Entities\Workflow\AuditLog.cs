using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Domain.Entities.Workflow;

/// <summary>
/// Represents a system-wide audit log entry for tracking significant events and changes.
/// Maps to the AuditLogs table in the database.
/// </summary>
public class AuditLog : BaseEntity<long>
{
    /// <summary>
    /// Timestamp when the event actually occurred (client-side if available from PWA sync, otherwise server time of event)
    /// </summary>
    public DateTime EventTimestamp { get; set; }

    /// <summary>
    /// Timestamp when the server processed and recorded this log entry
    /// </summary>
    public DateTime ServerReceivedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// User who performed the action (CUID FK). NULL for system-generated events.
    /// </summary>
    [MaxLength(25)]
    public string? UserId { get; set; }

    /// <summary>
    /// Navigation property for the user (optional)
    /// </summary>
    public virtual User? User { get; set; }

    /// <summary>
    /// The type of entity affected (e.g., "Audit", "User", "Finding")
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// Primary Key (CUID or integer) of the entity that was affected
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string EntityId { get; set; } = string.Empty;

    /// <summary>
    /// Type of action performed (e.g., "Create", "Update", "Delete", "StatusChange", "LoginSuccess")
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string ActionType { get; set; } = string.Empty;

    /// <summary>
    /// JSON or XML representation of data before the change, if applicable
    /// </summary>
    public string? OldValues { get; set; }

    /// <summary>
    /// JSON or XML representation of data after the change, if applicable
    /// </summary>
    public string? NewValues { get; set; }

    /// <summary>
    /// Additional context, comments, or system-generated information about the log entry
    /// </summary>
    public string? Details { get; set; }

    /// <summary>
    /// IP address from which the action originated, if available
    /// </summary>
    [MaxLength(45)]
    public string? IPAddress { get; set; }

    /// <summary>
    /// Version of the application that made the change, if available
    /// </summary>
    [MaxLength(50)]
    public string? AppVersion { get; set; }

    /// <summary>
    /// Indicates if this was a system-generated event
    /// </summary>
    public bool IsSystemGenerated => string.IsNullOrEmpty(UserId);

    /// <summary>
    /// Gets a summary description of the log entry
    /// </summary>
    public string GetSummary()
    {
        var userInfo = IsSystemGenerated ? "System" : User?.FullName ?? "Unknown User";
        return $"{userInfo} performed {ActionType} on {EntityType} {EntityId}";
    }
}
