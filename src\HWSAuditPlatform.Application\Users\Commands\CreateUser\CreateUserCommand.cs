using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Users.Commands.CreateUser;

/// <summary>
/// Command to create a new user
/// </summary>
public class CreateUserCommand : BaseCommand<string>
{
    public string Username { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public UserRole Role { get; set; }
    public int? FactoryId { get; set; }
    public bool IsActive { get; set; } = true;
    public string? AdObjectGuid { get; set; }
    public string? AdDistinguishedName { get; set; }
}
