using MediatR;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Application.Behaviors;

/// <summary>
/// Pipeline behavior for publishing domain events after successful command execution
/// </summary>
/// <typeparam name="TRequest">The request type</typeparam>
/// <typeparam name="TResponse">The response type</typeparam>
public class DomainEventBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly IApplicationDbContext _context;
    private readonly IDomainEventService _domainEventService;

    public DomainEventBehavior(IApplicationDbContext context, IDomainEventService domainEventService)
    {
        _context = context;
        _domainEventService = domainEventService;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var response = await next();

        // Collect domain events from all entities
        var domainEvents = GetDomainEvents();

        // Clear domain events from entities
        ClearDomainEvents();

        // Publish domain events
        if (domainEvents.Any())
        {
            await _domainEventService.PublishAsync(domainEvents, cancellationToken);
        }

        return response;
    }

    private List<IDomainEvent> GetDomainEvents()
    {
        var domainEvents = new List<IDomainEvent>();

        // Get all tracked entities that have domain events
        var entities = _context.GetType()
            .GetProperties()
            .Where(p => p.PropertyType.IsGenericType && 
                       p.PropertyType.GetGenericTypeDefinition() == typeof(Microsoft.EntityFrameworkCore.DbSet<>))
            .SelectMany(p => ((IEnumerable<object>)p.GetValue(_context)!).OfType<BaseEntity>())
            .Where(e => e.DomainEvents.Any())
            .ToList();

        foreach (var entity in entities)
        {
            domainEvents.AddRange(entity.DomainEvents);
        }

        return domainEvents;
    }

    private void ClearDomainEvents()
    {
        var entities = _context.GetType()
            .GetProperties()
            .Where(p => p.PropertyType.IsGenericType && 
                       p.PropertyType.GetGenericTypeDefinition() == typeof(Microsoft.EntityFrameworkCore.DbSet<>))
            .SelectMany(p => ((IEnumerable<object>)p.GetValue(_context)!).OfType<BaseEntity>())
            .Where(e => e.DomainEvents.Any())
            .ToList();

        foreach (var entity in entities)
        {
            entity.ClearDomainEvents();
        }
    }
}
