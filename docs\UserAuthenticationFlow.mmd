sequenceDiagram
    participant User
    participant ClientApp as Client App (PWA/Portal)
    participant API_Service as Web API
    participant IDP as Identity Provider (e.g., ADFS)
    participant AD as Active Directory
    participant HWS<PERSON>_DB as HWSAP DB

    User->>ClientApp: 1. Accesses App / Attempts Action
    ClientApp->>IDP: 2. Redirects to IDP for Login (if not authenticated)
    User->>IDP: 3. Enters AD Credentials
    IDP->>AD: 4. Authenticates Credentials
    AD-->>IDP: Authentication Success/Failure
    IDP-->>ClientApp: 5. Returns Token (e.g., JWT) / Auth Code
    ClientApp->>API_Service: 6. Makes API Request with Token
    API_Service->>IDP: 7. Validates Token (checks signature, expiry, issuer)
    IDP-->>API_Service: Token Valid
    API_Service->>HWSAP_DB: 8. (Optional) Fetches User's App Roles/Permissions (from Users table synced from AD)
    HWSAP_DB-->>API_Service: User App Roles
    API_Service-->>ClientApp: 9. Processes Request & Returns Response (if authorized)