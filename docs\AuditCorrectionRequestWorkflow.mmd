stateDiagram-v2
    [*] --> Submitted_Audit : Auditor completes audit
    Submitted_Audit --> Pending_Correction_Request : Auditor requests correction
    state Pending_Correction_Request {
        [*] --> Awaiting_Manager_Approval : Request submitted
        Awaiting_Manager_Approval --> Approved : Manager Approves
        Awaiting_Manager_Approval --> Denied : Manager Denies
    }
    Approved --> Audit_Pending_Correction : Audit status updated
    Audit_Pending_Correction --> Changes_Submitted_By_Auditor : Auditor makes changes & resubmits
    Changes_Submitted_By_Auditor --> Pending_Manager_Review_Again : Audit goes back for review
    Pending_Manager_Review_Again --> Manager_Reviewed_Closed : Manager reviews & closes
    Pending_Manager_Review_Again --> Audit_Pending_Correction : Manager requests further corrections
    Denied --> Submitted_Audit : Request denied, audit remains as is
    Manager_Reviewed_Closed --> [*]