using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Entities.Organization;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.ValueObjects;
using HWSAuditPlatform.Tests.Common;

namespace HWSAuditPlatform.Tests.Infrastructure.Persistence;

public class ApplicationDbContextTests : BaseDbTestClass
{
    [Fact]
    public async Task SaveChangesAsync_ShouldSetAuditFields()
    {
        // Arrange
        var user = User.Create(
            username: "testuser",
            firstName: "Test",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        var beforeSave = DateTime.UtcNow;

        // Act
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().BeAfter(beforeSave.AddSeconds(-1));
        user.UpdatedAt.Should().BeAfter(beforeSave.AddSeconds(-1));
        user.RecordVersion.Should().Be(1);
    }

    [Fact]
    public async Task SaveChangesAsync_OnUpdate_ShouldUpdateTimestampAndVersion()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var user = await Context.Users.FirstAsync();
        var originalCreatedAt = user.CreatedAt;
        var originalVersion = user.RecordVersion;
        
        // Wait a bit to ensure timestamp difference
        await Task.Delay(10);

        // Act
        user.UpdateEmail("<EMAIL>", "updater");
        await Context.SaveChangesAsync();

        // Assert
        user.CreatedAt.Should().Be(originalCreatedAt); // Should not change
        user.UpdatedAt.Should().BeAfter(originalCreatedAt);
        user.RecordVersion.Should().Be(originalVersion + 1);
    }

    [Fact]
    public async Task Users_ShouldEnforceUniqueUsername()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var duplicateUser = User.Create(
            username: "admin", // Duplicate username
            firstName: "Duplicate",
            lastName: "User",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        Context.Users.Add(duplicateUser);

        // Act & Assert
        await Assert.ThrowsAsync<DbUpdateException>(() => Context.SaveChangesAsync());
    }

    [Fact]
    public async Task Users_ShouldEnforceUniqueEmail()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var duplicateUser = User.Create(
            username: "newuser",
            firstName: "New",
            lastName: "User",
            email: "<EMAIL>", // Duplicate email
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        Context.Users.Add(duplicateUser);

        // Act & Assert
        await Assert.ThrowsAsync<DbUpdateException>(() => Context.SaveChangesAsync());
    }

    [Fact]
    public async Task Factories_ShouldStoreAddressValueObject()
    {
        // Arrange
        var location = new Location
        {
            Id = 1,
            LocationName = "Test Location",            
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var factory = new Factory
        {
            Id = 1,
            FactoryName = "Test Factory",
            FactoryProcess = "Test Process",
            LocationId = 1,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        // Act
        Context.Locations.Add(location);
        Context.Factories.Add(factory);
        await Context.SaveChangesAsync();

        // Assert
        var savedLocation = await Context.Locations.FirstAsync();
        savedLocation.Address.Should().NotBeNull();
        savedLocation.Address.AddressLine1.Should().Be("123 Test St");
        savedLocation.Address.City.Should().Be("Test City");
        savedLocation.Address.State.Should().Be("Test State");
        savedLocation.Address.PostalCode.Should().Be("12345");
        savedLocation.Address.Country.Should().Be("Test Country");
    }

    [Fact]
    public async Task Roles_ShouldHaveSeedData()
    {
        // Arrange & Act
        var roles = await Context.Roles.ToListAsync();

        // Assert
        roles.Should().HaveCount(3);
        roles.Should().Contain(r => r.RoleName == UserRole.Admin);
        roles.Should().Contain(r => r.RoleName == UserRole.Manager);
        roles.Should().Contain(r => r.RoleName == UserRole.Auditor);
    }

    [Fact]
    public async Task Users_ShouldLoadRelatedData()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var users = await Context.Users
            .Include(u => u.Role)
            .Include(u => u.Factory)
            .ToListAsync();

        // Assert
        users.Should().NotBeEmpty();
        users.Should().OnlyContain(u => u.Role != null);
        users.Where(u => u.FactoryId.HasValue).Should().OnlyContain(u => u.Factory != null);
    }

    [Fact]
    public async Task Context_ShouldSupportConcurrentAccess()
    {
        // Arrange
        await SeedTestDataAsync();
        
        var user1 = await Context.Users.FirstAsync();
        var user2 = await Context.Users.FirstAsync();

        // Act
        user1.UpdateEmail("<EMAIL>", "updater1");
        user2.UpdateEmail("<EMAIL>", "updater2");

        await Context.SaveChangesAsync();

        // Assert - Second update should fail due to concurrency
        await Assert.ThrowsAsync<DbUpdateConcurrencyException>(() => Context.SaveChangesAsync());
    }

    [Fact]
    public async Task Context_ShouldHandleNullValues()
    {
        // Arrange
        var user = User.Create(
            username: "nulltest",
            firstName: null, // Null first name
            lastName: null,  // Null last name
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null, // Null factory
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        // Act
        Context.Users.Add(user);
        await Context.SaveChangesAsync();

        // Assert
        var savedUser = await Context.Users.FirstAsync(u => u.Username == "nulltest");
        savedUser.FirstName.Should().BeNull();
        savedUser.LastName.Should().BeNull();
        savedUser.FactoryId.Should().BeNull();
        savedUser.AdObjectGuid.Should().BeNull();
        savedUser.AdDistinguishedName.Should().BeNull();
    }

    [Fact]
    public async Task Context_ShouldSupportTransactions()
    {
        // Arrange
        using var transaction = await Context.Database.BeginTransactionAsync();
        
        var user = User.Create(
            username: "transactiontest",
            firstName: "Transaction",
            lastName: "Test",
            email: "<EMAIL>",
            roleId: 1,
            factoryId: null,
            isActive: true,
            adObjectGuid: null,
            adDistinguishedName: null,
            createdByUserId: "creator"
        );

        // Act
        Context.Users.Add(user);
        await Context.SaveChangesAsync();
        
        // Rollback transaction
        await transaction.RollbackAsync();

        // Assert
        var userExists = await Context.Users.AnyAsync(u => u.Username == "transactiontest");
        userExists.Should().BeFalse();
    }

    [Fact]
    public async Task Context_ShouldHandleLargeDataSets()
    {
        // Arrange
        var users = new List<User>();
        for (int i = 0; i < 100; i++)
        {
            var user = User.Create(
                username: $"user{i}",
                firstName: $"First{i}",
                lastName: $"Last{i}",
                email: $"user{i}@test.com",
                roleId: 1,
                factoryId: null,
                isActive: true,
                adObjectGuid: null,
                adDistinguishedName: null,
                createdByUserId: "creator"
            );
            users.Add(user);
        }

        // Act
        Context.Users.AddRange(users);
        await Context.SaveChangesAsync();

        // Assert
        var userCount = await Context.Users.CountAsync();
        userCount.Should().Be(100);
    }
}
