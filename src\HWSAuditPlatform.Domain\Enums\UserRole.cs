namespace HWSAuditPlatform.Domain.Enums;

/// <summary>
/// Represents the different user roles in the audit platform.
/// Maps to the user_role enum in the database.
/// </summary>
public enum UserRole
{
    /// <summary>
    /// Administrator with full system access
    /// </summary>
    Admin,

    /// <summary>
    /// Manager with audit review and management capabilities
    /// </summary>
    Manager,

    /// <summary>
    /// Auditor who performs audits in the field
    /// </summary>
    Auditor
}
