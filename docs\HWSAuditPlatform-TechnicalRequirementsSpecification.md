# HWS Audit Platform: Technical Requirements Specification

- **Version:** 1.0
- **Date:** 2025-06-02
- **Project:** HWS Audit Platform
- **Status:** Initial Draft

**Table of Contents:**

1. Introduction
2. System Architecture
3. Database Design (AuditFlowDB)
4. Core Modules and Functionalities
5. Non-Functional Requirements
6. Technology Stack (Proposed)
7. API Design Considerations
8. SchedulerWorker Service
9. Appendices

---

## **1. Introduction**

### **1.1. Purpose**

This document outlines the technical requirements for the HWS Audit Platform. It serves as a blueprint for the development team, defining the system's architecture, functionalities, data structures, and non-functional characteristics. The platform aims to modernize and streamline internal audit processes within HWS.

### **1.2. Project Goals**

- To develop a robust, scalable, and secure platform for managing the entire audit lifecycle.
- To provide a centralized system for audit planning, scheduling, assignment, and tracking.
- To empower auditors with an efficient, tablet-optimized Progressive Web App (PWA) capable of full offline audit execution.
- To ensure data integrity and consistency, especially with offline data synchronization.
- To facilitate the management of findings and corrective actions (CAPA).
- To enable automated scheduling of recurring audits.
- To provide comprehensive logging for audit trails and system monitoring.

### **1.3. Scope**

#### **1.3.1. In Scope:**

- User and role management, with primary user/group provisioning via Active Directory (AD).
- Organizational structure definition (Locations, Factories, Areas, Sub-Areas) – initially managed within the platform, with future plans to integrate with an existing organizational database.
- Audit template creation, versioning, and management with diverse question types and conditional logic.
- Audit scheduling (manual and recurring) and assignment (individual and group-based).
- Audit execution via a PWA with offline capabilities, including data and evidence capture.
- Data synchronization between the PWA and the central database.
- Audit review, approval, and correction workflows.
- Findings management. Corrective action management within this platform will initially be a placeholder, with future plans to integrate with or be superseded by the dedicated "Action Manager (AM)" system.
- System-wide audit logging.
- A Blazor Server application for management and administrative functions.
- A Blazor PWA for auditor field operations.
- A dedicated SchedulerWorker service for background tasks (e.g., recurring audit generation, AD sync).
- A Web API to serve both frontend applications and facilitate communication.

#### **1.3.2. Out of Scope (for initial version, unless specified otherwise):**

- Advanced business intelligence and predictive analytics dashboards (basic reporting is in scope).
- Direct integration with other enterprise systems beyond AD (unless specified).
- Real-time collaboration features within an audit (e.g., multiple auditors on the same audit instance simultaneously).
- Public-facing interfaces.

### **1.4. Definitions, Acronyms, and Abbreviations**

- **AD:** Active Directory
- **API:** Application Programming Interface
- **CAPA:** Corrective And Preventive Action
- **CUID:** Collision-Resistant Unique Identifier
- **DBML:** Database Markup Language
- **HWS:** (Implied: The organization for which the platform is built)
- **PWA:** Progressive Web App
- **RBAC:** Role-Based Access Control
- **UI:** User Interface
- **UX:** User Experience

### **1.5. Document Conventions**

- Mathematical expressions will be rendered using LaTeX.
- Code snippets will be formatted and language-tagged.
- Requirements are indicated by "The system shall..." or "The [component] must...".

---

## **2. System Architecture**

### **2.1. Overview**

The HWS Audit Platform will adopt a layered architecture to promote separation of concerns, maintainability, and scalability. The primary components are:



### **2.2. Components**

#### **2.2.1. Domain Layer**

- **Purpose:** Contains the core business logic, entities, value objects, and domain events. It represents the heart of the audit management system, independent of application or infrastructure concerns.
- **Key Responsibilities:** Enforcing business rules, defining domain models (e.g., Audit, Template, Question, Finding), and ensuring data consistency at the entity level.
- **Technology:** Plain C# classes, potentially using patterns like DDD (Domain-Driven Design).

#### **2.2.2. Application Layer**

- **Purpose:** Orchestrates use cases and application-specific logic. It acts as an intermediary between the UI/API layers and the Domain/Infrastructure layers.
- **Key Responsibilities:** Defining application services, handling commands and queries, managing transactions, coordinating with the domain layer, and interacting with infrastructure services (e.g., repositories, notification services).
- **Technology:** C# classes, MediatR or similar patterns for CQRS.

#### **2.2.3. Infrastructure Layer**

- **Purpose:** Implements interfaces defined in the Application or Domain layers for concerns like data persistence, external service communication, and other technical functionalities.
- **Key Responsibilities:**
  - Data access implementation (e.g., Entity Framework Core repositories for AuditFlowDB).
  - Integration with Active Directory for user/group synchronization.
  - File storage interaction (e.g., Local File Storage, local file system for `AuditAttachments`).
  - Implementation of cross-cutting concerns like logging.
- **Technology:** Entity Framework Core, AD integration libraries.

#### **2.2.4. API (Web API)**

- **Purpose:** Provides a RESTful HTTP interface for the frontend applications (Blazor Server and PWA) and potentially other future clients.
- **Key Responsibilities:** Exposing application functionalities, handling HTTP requests/responses, authentication, authorization, input validation, and data transformation (DTOs).
- **Technology:** ASP.NET Core Web API.

![High-Level API Interaction Overview](High-LevelAPIInteractionOverview.svg)

#### **2.2.5. Blazor Server (Management Portal)**

- **Purpose:** A web application for administrators and managers to configure the system, create and assign audits, monitor progress, review submissions, and manage findings.
- **Key Responsibilities:** Providing a rich, interactive UI for management tasks, real-time updates (leveraging SignalR inherent in Blazor Server), and secure access to administrative functions.
- **Technology:** Blazor Server.

#### **2.2.6. Blazor PWA (Auditor Field App)**

- **Purpose:** A Progressive Web App optimized for tablet devices, enabling auditors to conduct audits, capture evidence, and work offline.
- **Key Responsibilities:**
  - Downloading assigned audit data for offline use.
  - Providing an intuitive UI for completing audit checklists.
  - Capturing various answer types and attaching evidence (photos, documents).
  - Storing audit data locally (e.g., using IndexedDB).
  - Synchronizing local data with the central server when online.
  - Handling CUID generation for new offline records.
- **Technology:** Blazor WebAssembly (PWA).

#### **2.2.7. SchedulerWorker**

- **Purpose:** A dedicated background service responsible for automated tasks.
- **Key Responsibilities:**
  - Generating audit instances based on `RecurringAuditSettings` and `RecurrenceRules`.
  - Potentially handling AD synchronization tasks on a schedule.
  - Other background maintenance tasks as needed.
- **Technology:** .NET Worker Service or a similar background processing framework (e.g., Hangfire, Quartz.NET).

---

## **3. Database Design (AuditFlowDB)**

### **3.1. Overview**
The system will utilize the AuditFlowDB, whose schema is defined in DBML version 1.6 (see Appendix 9.1 or provided schema). This relational database is designed to support all functionalities of the HWS Audit Platform, including offline data synchronization.

### **3.2. Key Design Principles**

#### **3.2.1. CUIDs for Client-Side Generation**

- Entities frequently created or modified on the client-side (PWA), especially offline, shall use CUIDs (varchar(25)) as primary keys (e.g., `Audits`, `AuditAnswers`, `AuditAttachments`, `Findings`). This allows for unique ID generation without server roundtrips, preventing PK conflicts during synchronization.

#### **3.2.2. Record Versioning for Optimistic Concurrency**

- Most tables involved in PWA data synchronization shall include a `RecordVersion` integer column (defaulting to 1). This field will be incremented on each update and used to implement optimistic concurrency control, helping to resolve conflicts during data sync.

#### **3.2.3. Integer PKs for Server-Managed Entities**

- Entities primarily managed server-side or with stable identities (e.g., `Roles`, `AuditTemplates`, `Questions`) shall use auto-incrementing integer primary keys.

#### **3.2.4. Enumerations**

- The database utilizes ENUM types for predefined sets of values (e.g., `user_role`, `question_type`, `audit_overall_status`), ensuring data consistency and readability. These will be mapped to corresponding enums in the Domain/Application layers.

### **3.3. Data Model Summary**

The database is organized into logical groups:

- **Core Access & Users:** `Roles`, `Users`, `UserGroups`, `UserGroupMembers`, `AdGroupRoleMapping`. Manages user identities, permissions, and AD integration.
- **Organizational Structure:** `Location`, `Factories`, `Areas`, `SubAreas`. Defines the physical hierarchy for audit targeting.
  - *Note:* `Factories.FactoryProcess` is currently denormalized. Future iterations should consider normalizing this to `Processes` and `FactoryProcesses` tables for improved querying and integrity.
- **Audit Definition & Templates:** `AuditTemplates`, `QuestionGroups`, `Questions`, `QuestionOptions`. Defines the structure and content of audit checklists.
  - *Note:* `Questions.AllowedEvidenceTypes` is currently a text field. Future iterations should consider normalizing this to `EvidenceTypes` and `QuestionAllowedEvidenceTypes` tables.
- **Audit Execution & Evidence:** `Audits`, `AuditAnswers`, `AuditAnswerSelectedOptions`, `AuditAnswerFailureReasons`, `AuditAttachments`. Captures data from performed audits.
- **Findings & Corrective Actions:** `Findings`, `CorrectiveActions`. Manages issues identified and their resolution.
- **Scheduling & Recurrence:** `RecurringAuditSettings`, `RecurrenceRules`. Manages automated audit generation.
- **Workflow & Logging:** `AuditCorrectionRequests`, `AuditLogs`. Supports audit modification workflows and system-wide event tracking.

### **3.4. Data Migration and Seeding**

- Initial data seeding scripts shall be provided for `Roles`, default `Location` data (if applicable), and any other foundational lookup data.
- Consideration must be given to data migration strategies for future schema updates.

---

## **4. Core Modules and Functionalities**

### **4.1. User Management & Authentication**

#### **4.1.1. User Registration & Profile Management**

- The system shall allow administrators to manage user accounts (`Users` table).
- Users shall have profiles including `FirstName`, `LastName`, `Email`.
- User accounts can be marked as `IsActive`.
- The system shall track `LastLoginDate`, `CreatedAt`, `UpdatedAt`.

#### **4.1.2. Role-Based Access Control (RBAC)**

- The system shall support predefined user roles (`Roles` table: Admin, Manager, Auditor).
- Each user shall be assigned a primary `RoleId`.
- Permissions for system functionalities shall be governed by these roles.

#### **4.1.3. User Group Management**

- Administrators/Managers shall be able to create and manage `UserGroups`.
- Users can be members of multiple groups (`UserGroupMembers` table).
- Groups can be used for audit assignment (`Audits.AssignedToUserGroupId`, `RecurringAuditSettings.AssignToUserGroupId`).

#### **4.1.4. Active Directory (AD) Integration**

- The system shall support synchronization of users and groups from AD.
  - `Users.AdObjectGuid`, `Users.AdDistinguishedName`, `Users.AdSyncLastDate` will store AD-specific attributes.
  - `UserGroups.AdObjectGuid`, `UserGroups.IsAdSynced` will store AD-specific attributes for groups.
- The system shall map AD groups to application roles using the `AdGroupRoleMapping` table.
- Authentication for AD-synced users should ideally leverage AD credentials (e.g., via ADFS).

![AD Integration](UserAuthenticationFlow.svg)

### **4.2. Organizational Structure Management**

- Administrators shall manage `Location`, `Factories`, `Areas`, and `SubAreas` through the Management Portal.
- Entities shall support `IsActive` flags.
- Relationships (e.g., Factory to Location, Area to Factory) must be maintained.
- `Factories.FactoryProcess`: The system shall allow storing a list of processes. Acknowledge current denormalization and plan for future normalization.

#### **4.3. Audit Template Management**

#### **4.3.1. Template Creation & Versioning**

- Authorized users (e.g., Admins, designated Managers) shall create and manage `AuditTemplates`.
- Templates shall have a `TemplateName`, `Description`, and `Version`. Versioning allows for iterative improvements without affecting in-progress audits based on older versions.
- Templates can be `IsPublished` (ready for use) and `IsActive` (available).

#### **4.3.2. Question Group Management**

- Templates can organize questions into `QuestionGroups` with `DisplayOrder`.

#### **4.3.3. Question Definition**

- Questions (`Questions` table) shall support:
  - `QuestionText` (candidate for multi-language support).
  - `QuestionType` (YesNo, Numeric, SingleSelect, MultiSelect, ShortText, LongText, Date).
  - `DisplayOrder`.
  - `IsRequired` flag.
  - Optional `Weight` for scoring.
  - `HelpText` (candidate for multi-language support).
  - Conditional logic: `ParentQuestionId` and `TriggerAnswerValue` to show/hide follow-up questions.
  - Default `SeverityLevel` for failed answers.
  - `EvidenceRequired`, `EvidenceInstructions` (candidate for multi-language), `EvidenceTimingHint`.
  - `AllowedEvidenceTypes`: Store allowed MIME types. Acknowledge current denormalization.

#### **4.3.4. Predefined Answer Options**

- For `SingleSelect` and `MultiSelect` question types, `QuestionOptions` shall be definable with `OptionText`, `OptionValue`, `IsCorrectOption`, and `DisplayOrder`.

#### **4.3.5. Template Publishing & Activation**

- Only `IsPublished` and `IsActive` templates shall be available for scheduling new audits.

### **4.4. Audit Lifecycle Management**

#### **4.4.1. Audit Scheduling (Manual & Recurring)**

- Managers shall schedule new `Audits` manually via the Management Portal, selecting a template, target (Factory, Area, SubArea), scheduled date, and due date.
- The `SchedulerWorker` service shall automatically generate `Audits` based on `RecurringAuditSettings` and `RecurrenceRules`.

#### **4.4.2. Audit Assignment**

- Audits shall support `AssignmentType`:
  - `Individual`: Assigned to a specific `AssignedToUserId`.
  - `GroupAny`: Assigned to an `AssignedToUserGroupId`; any member can claim it (updating `AssignedToUserId`).
  - `GroupAllScheduled` (for recurring settings): The scheduler creates individual audits for each member of the specified group.
- The PWA shall allow auditors to view audits assigned to them or their groups.

#### **4.4.3. Audit Execution (PWA - Online/Offline)**

- Auditors shall use the Blazor PWA to conduct audits.
- The PWA must download assigned audit data (template structure, questions, existing answers if resuming) for offline use.
- Auditors shall be able to start, pause, and resume audits. `Audits.StartedAt` tracks the start.

#### **4.4.4. Answer & Evidence Capturing**

- For each question in an audit, auditors shall record an `AuditAnswer`.
  - Answers can be `AnswerBoolean`, `AnswerText`, `AnswerNumeric`, `AnswerDate`, or linked to `SelectedOptionId` (for SingleSelect) or `AuditAnswerSelectedOptions` (for MultiSelect).
  - Questions can be marked as `IsNotApplicable` with mandatory `Comments` for justification.
  - `AuditAnswerFailureReasons` can capture multiple reasons for a failed/negative answer.
- If `Questions.EvidenceRequired` is true, auditors must attach evidence.
- `AuditAttachments` shall store metadata about evidence files (`FileName`, `ContentType`, `FileSize`). Actual files will be stored in a configurable blob storage.
  - The PWA must support capturing photos directly and uploading various file types.

#### **4.4.5. Audit Submission & Status Tracking**

- Auditors shall submit completed audits via the PWA. `Audits.CompletedAt` is recorded.
- The `Audits.OverallStatus` shall reflect the audit's lifecycle stage (Scheduled, InProgress, Submitted, PendingManagerReview, PendingCorrection, ManagerReviewed, Closed, Cancelled).

#### **4.4.6. Audit Review & Approval (Management Portal)**

- Managers shall review submitted audits in the Management Portal.
- Managers can add `ManagerComments`.
- Managers can change the status (e.g., to `ManagerReviewed`, `PendingCorrection`, or `Closed`). `ReviewedByUserId` and `ReviewedAt` are recorded.

#### **4.4.7. Audit Correction Workflow**

- Auditors can request corrections to their submitted audits via `AuditCorrectionRequests`.
- Requests include a `RequestReason` and have a `correction_request_status` (PendingApproval, Approved, Denied, ChangesSubmitted).
- Managers review and approve/deny requests, providing `ManagerComments`.
- If approved, the audit status changes (e.g., to `PendingCorrection`), allowing the auditor to make changes and resubmit.

![Audit Correction Request Workflow](AuditCorrectionRequestWorkflow.svg)

### **4.5. Findings & Corrective Action (CAPA) Management**

#### **4.5.1. Finding Creation & Association**

- Findings (`Findings` table) can be generated from `AuditAnswers` that indicate non-conformity.
- Findings shall have a `FindingCode`, `FindingDescription`, `FindingSeverityLevel`, and `finding_status`.

#### **4.5.2. Severity Assessment**

- The `FindingSeverityLevel` (Critical, Major, Minor, Observation) shall be assigned based on question defaults or auditor/manager input.

#### **4.5.3. Corrective Action Planning & Assignment**

- For each finding, one or more `CorrectiveActions` can be created.
- Actions include `ActionDescription`, `AssignedToUserId`, `DueDate`, and `corrective_action_status`.

#### **4.5.4. Status Tracking & Verification**

- The system shall track the status of findings and corrective actions through their respective lifecycles (e.g., `Open` to `Closed` for findings; `Assigned` to `VerifiedClosed` for actions).
- `CompletionDate` and `EvidenceNotes` are recorded for corrective actions.

### **4.6. Reporting & Analytics (High-Level Requirements)**

- The Management Portal shall provide basic reporting capabilities:
  - Audit completion status.
  - Overdue audits and corrective actions.
  - Summary of findings by severity, area, etc.
- The system should be designed to allow for future expansion with more advanced analytics.

### **4.7. System Logging & Auditing**

- The `AuditLogs` table shall record significant system events and entity changes.
- Logs must include `EventTimestamp`, `ServerReceivedAt`, `UserId` (if applicable), `EntityType`, `EntityId`, `ActionType`, `OldValues`, `NewValues`, `Details`, `IPAddress`, and `AppVersion`.
- Logs are immutable and primarily server-generated or consolidated from PWA activity.

### **4.8. PWA Offline Capability & Data Synchronization**

![image](PWADataSynchronizationFlow.svg)

#### **4.8.1. Data Download to PWA**

- The PWA shall download all necessary data for assigned audits when online:
  - Audit instance details (`Audits` table).
  - Relevant `AuditTemplate`, `QuestionGroups`, `Questions`, `QuestionOptions`.
  - Associated `Factory`, `Area`, `SubArea` information.
  - Existing `AuditAnswers` and `AuditAttachments` if resuming an audit.
  - Relevant lookup data (e.g., user lists for assignment if applicable offline).

#### **4.8.2. Local Data Storage (PWA)**

- The PWA shall use browser local storage mechanisms (e.g., IndexedDB) to store downloaded data and any new/modified data created offline.
- CUIDs shall be generated client-side for new records (e.g., new `AuditAnswers`, `AuditAttachments`, `Findings`).

#### **4.8.3. Conflict Resolution Strategy**

- The `RecordVersion` field shall be used for optimistic concurrency.
- During synchronization:
  - If PWA `RecordVersion` matches server `RecordVersion` for an update, the update proceeds, and server `RecordVersion` is incremented.
  - If PWA `RecordVersion` is less than server `RecordVersion`, a conflict exists. The system must define a resolution strategy (e.g., last-write-wins with PWA data, or flag for manual resolution, or a more sophisticated merge if feasible). For simplicity, a "PWA wins if newer timestamp, else server wins" or a "flag for manager review" might be initial approaches. This needs careful design.
  - For new records created offline (identified by CUIDs not yet on the server), they are simply inserted.

#### **4.8.4. Data Upload & Synchronization Logic**

- The PWA shall provide a mechanism to initiate synchronization when online.
- Sync process should be robust, handling potential network interruptions and resuming.
- A queueing mechanism on the PWA for outgoing changes is recommended.
- The API will provide dedicated endpoints for receiving synchronized data batches.

### **4.9. Notifications (Consideration)**

- The system should be designed to potentially incorporate a notification system (e.g., email, in-app) for events like:
  - New audit assignment.
  - Audit submission/review.
  - Overdue tasks.
  - Finding/Corrective Action updates.
  - (This is a placeholder for future enhancement if not in V1).

---

## **5. Non-Functional Requirements**

### **5.1. Performance**

- Management Portal: Page loads should generally be within 3 seconds. Complex data grids may take longer but should show loading indicators.
- PWA: Application load time (after initial cache) should be under 5 seconds. Audit form navigation and data entry must be responsive (sub-second).
- API: Average response time for typical requests should be < 500ms under normal load.
- Data Synchronization: Syncing a typical audit (e.g., 50 questions, 10 photo attachments) should complete within a reasonable timeframe (e.g., < 1-2 minutes on a stable connection).

### **5.2. Scalability**

- The system should be designed to support hundreds of concurrent users (auditors and managers).
- The database should handle tens of thousands of audits and millions of answers/attachments over time.
- The architecture should allow for scaling out API and worker services.

### **5.3. Reliability & Availability**

- Target uptime for server-side components: 99.9%.
- The PWA must function reliably offline once necessary data is downloaded.
- Data synchronization must be robust, with mechanisms to prevent data loss.

### **5.4. Security**

- All communication between clients (PWA, Portal) and API must use HTTPS.
- Authentication: Secure credential management. Integration with AD for enterprise identity.
- Authorization: Enforce RBAC strictly at the API level.
- Data Protection: Protect against common web vulnerabilities (OWASP Top 10). Encrypt sensitive data at rest if necessary (e.g., attachment storage).
- Input Validation: Perform validation on both client and server sides.
- Audit Logs: Secure and tamper-evident logging.

### **5.5. Usability**

- **Management Portal:** Intuitive navigation, clear presentation of data, efficient workflows for administrative tasks.
- **PWA:** Optimized for tablet touch interfaces. Clear, large fonts and controls. Minimal clicks to perform common actions. Clear indication of offline status and sync progress.

### **5.6. Maintainability**

- Code should be well-documented, follow consistent coding standards, and be organized logically within the layered architecture.
- Automated tests (unit, integration) should be implemented.
- Configuration should be externalized.

### **5.7. Data Integrity**

- Database constraints (PK, FK, unique, not null) must be enforced.
- Business rules in the Domain layer must ensure data consistency.
- Optimistic concurrency control (`RecordVersion`) for synchronized data.

### **5.8. Offline Operation (PWA Specific)**

- The PWA must allow completion of entire audits while offline.
- All data captured offline must be stored securely and reliably on the device until synced.
- Clear feedback to the user about offline status and data pending synchronization.

---

## **6. Technology Stack (Proposed)**

- **Backend (API, Application, Domain, Infrastructure):** .NET 8 (or latest LTS)
- **Web API Framework:** ASP.NET Core Web API
- **Management Portal Frontend:** Blazor Server (.NET 8)
- **Auditor Field App Frontend:** Blazor WebAssembly PWA (.NET 8)
- **Database:** SQL Server (or other relational DB compatible with EF Core, e.g., PostgreSQL)
- **ORM:** Entity Framework Core 8
- **Background Processing (SchedulerWorker):** .NET Worker Service (potentially with Hangfire or Quartz.NET for advanced scheduling)
- **Real-time Communication (Blazor Server):** SignalR (built-in)
- **Authentication:** ASP.NET Core Identity, OpenID Connect/OAuth 2.0 for AD integration.
- **PWA Local Storage:** IndexedDB (via Blazor's JS interop)---

## **7. API Design Considerations**

### **7.1. RESTful Principles**

- The API shall adhere to RESTful design principles, using standard HTTP methods (GET, POST, PUT, DELETE, PATCH), status codes, and resource-based URLs.

### **7.2. Authentication & Authorization**

- All API endpoints (except potentially a health check) must require authentication (e.g., JWT Bearer tokens).
- Authorization policies based on user roles shall protect access to resources and actions.

### **7.3. Versioning**

- The API should be versioned (e.g., URL path versioning: `/api/v1/...`) to allow for future evolution without breaking existing clients.

### **7.4. Key Endpoint Groups (Examples)**

- `/users`, `/roles`, `/usergroups`
- `/locations`, `/factories`, `/areas`, `/subareas`
- `/audittemplates`, `/questions`
- `/audits` (for CRUD, status updates, assignments)
- `/auditanswers`, `/auditattachments` (potentially batched for sync)
- `/findings`, `/correctiveactions`
- `/recurringauditsettings`
- `/auditcorrectionrequests`
- `/sync` (dedicated endpoints for PWA data synchronization)

---

## **8. SchedulerWorker Service**

### **8.1. Responsibilities**

![SchedulerWorker Sequence Diagram](RecurringAuditGenerationFlow.svg)

#### **8.1.1. Processing RecurringAuditSettings**

- The service shall periodically query `RecurringAuditSettings` that are `IsEnabled` and where `NextGenerationDate` is due.

#### **8.1.2. Generating Audit Instances**

- For each due setting, it shall:
  - Use the specified `AuditTemplateId`.
  - Determine the target `FactoryId`, `AreaId`, `SubAreaId` (these need to be part of `RecurringAuditSettings` or derived). *Correction: The DB schema for `RecurringAuditSettings` does not directly store Factory/Area/SubArea. This implies either the setting is generic and applies to many, or these need to be added to the `RecurringAuditSettings` table or a linking table.* For now, assume these are defined within the setting.
  - Calculate `ScheduledDate` (current date/time of generation) and `DueDate` (`ScheduledDate` + `DeadlineDays`).
  - Handle `AssignmentType`:
    - `Individual`: Create one `Audits` record assigned to `AssignToUserId`.
    - `GroupAny`: Create one `Audits` record assigned to `AssignToUserGroupId`.
    - `GroupAllScheduled`: Retrieve all users in `AssignToUserGroupId` and create an individual `Audits` record for each user.
  - Set initial `OverallStatus` to `Scheduled`.

#### **8.1.3. Updating NextGenerationDate**

- After successful generation, the service shall calculate and update the `NextGenerationDate` on the `RecurringAuditSetting` based on its `RecurrenceRules`.
- Update `LastGeneratedAt`.

#### **8.1.4. AD Sync (Potential Responsibility)**

- The SchedulerWorker may also be responsible for periodically running AD user and group synchronization tasks if not handled by a separate trigger/process.

### **8.2. Triggers & Scheduling**

- The service will run continuously as a background process.
- Internal timers or a scheduling library (e.g., Quartz.NET) will trigger jobs at configurable intervals (e.g., every few minutes or hourly) to check for tasks like recurring audit generation or AD sync.

---

## **9. Appendices**

### **9.1. AuditFlowDB Schema (DBML v1.6)**

Database schema in file: [File](database.dbml)