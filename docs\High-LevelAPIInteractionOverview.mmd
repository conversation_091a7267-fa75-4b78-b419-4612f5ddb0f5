graph LR
    subgraph Clients
        PWA[B<PERSON><PERSON> PWA]
        Portal[Blazor Server Portal]
        Scheduler[SchedulerWorker]
    end

    subgraph API_Endpoints [Web API]
        AuthN_AuthZ[Authentication & Authorization Endpoints]
        UserMgmt[User & Group Management Endpoints]
        OrgStruct[Organizational Structure Endpoints]
        TemplateMgmt[Audit Template & Question Endpoints]
        AuditExec[Audit Execution & Scheduling Endpoints]
        SyncData[PWA Data Sync Endpoints]
        FindingsCAPA[Findings & CAPA Endpoints Placeholder]
        Logging[Logging Endpoints]
    end

    PWA -->|HTTPS| AuthN_AuthZ
    PWA -->|HTTPS| AuditExec
    PWA -->|HTTPS| SyncData
    PWA -->|HTTPS| FindingsCAPA

    Portal -->|HTTPS/SignalR| AuthN_AuthZ
    Portal -->|HTTPS/SignalR| UserMgmt
    Portal -->|HTTPS/SignalR| OrgStruct
    Portal -->|HTTPS/SignalR| TemplateMgmt
    Portal -->|HTTPS/SignalR| AuditExec
    Portal -->|HTTPS/SignalR| FindingsCAPA

    Scheduler -->|Internal Call/HTTPS| AuditExec
    Scheduler -->|Internal Call/HTTPS| UserMgmt
    Scheduler -->|Internal Call/HTTPS| Logging

    %% Common interactions
    UserMgmt <--> HWSAP_DB[(HWSAP DB)]
    OrgStruct <--> HWSAP_DB
    TemplateMgmt <--> HWSAP_DB
    AuditExec <--> HWSAP_DB
    SyncData <--> HWSAP_DB
    FindingsCAPA <--> HWSAP_DB
    Logging <--> HWSAP_DB

    classDef client fill:#D6EAF8,stroke:#5DADE2,stroke-width:2px;
    classDef api fill:#D1F2EB,stroke:#48C9B0,stroke-width:2px;
    class PWA,Portal,Scheduler client;
    class AuthN_AuthZ,UserMgmt,OrgStruct,TemplateMgmt,AuditExec,SyncData,FindingsCAPA,Logging api;