﻿<Project Sdk="Microsoft.NET.Sdk">
<Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>e17dcf67-bb9e-4366-934d-8c6f81017d5d</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\HWSAuditPlatform.ApiService\HWSAuditPlatform.ApiService.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.SchedulerWorker\HWSAuditPlatform.SchedulerWorker.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.Web\HWSAuditPlatform.Web.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" VersionOverride="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" VersionOverride="9.0.5" />
  </ItemGroup>
</Project>