using HWSAuditPlatform.Application.DTOs;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.DTOs;

/// <summary>
/// Data Transfer Object for Audit entity
/// </summary>
public class AuditDto : AuditableDto<string>
{
    public int AuditTemplateId { get; set; }
    public string? AuditTemplateName { get; set; }
    public AssignmentType AssignmentType { get; set; }
    public string? AssignedToUserGroupId { get; set; }
    public string? AssignedToUserGroupName { get; set; }
    public string? AssignedToUserId { get; set; }
    public string? AssignedToUserName { get; set; }
    public DateTime ScheduledDate { get; set; }
    public DateTime? DueDate { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public AuditOverallStatus OverallStatus { get; set; }
    public int FactoryId { get; set; }
    public string? FactoryName { get; set; }
    public int AreaId { get; set; }
    public string? AreaName { get; set; }
    public int? SubAreaId { get; set; }
    public string? SubAreaName { get; set; }
    public decimal? OverallScore { get; set; }
    public string? ManagerComments { get; set; }
    public string? ReviewedByUserId { get; set; }
    public string? ReviewedByUserName { get; set; }
    public DateTime? ReviewedAt { get; set; }
    public string? RecurringAuditSettingId { get; set; }
    
    // Computed properties
    public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.UtcNow && OverallStatus != AuditOverallStatus.Closed && OverallStatus != AuditOverallStatus.Cancelled;
    public bool IsInProgress => OverallStatus == AuditOverallStatus.InProgress;
    public bool CanBeStarted => OverallStatus == AuditOverallStatus.Scheduled;
    public bool CanBeSubmitted => OverallStatus == AuditOverallStatus.InProgress;
}

/// <summary>
/// Simplified Audit DTO for lists and dashboards
/// </summary>
public class AuditSummaryDto
{
    public string Id { get; set; } = string.Empty;
    public string? AuditTemplateName { get; set; }
    public string? AssignedToUserName { get; set; }
    public DateTime ScheduledDate { get; set; }
    public DateTime? DueDate { get; set; }
    public AuditOverallStatus OverallStatus { get; set; }
    public string? FactoryName { get; set; }
    public string? AreaName { get; set; }
    public decimal? OverallScore { get; set; }
    public bool IsOverdue { get; set; }
}
