using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Domain.Entities.Findings;

/// <summary>
/// Represents a corrective action to address a finding.
/// Maps to the CorrectiveActions table in the database.
/// </summary>
public class CorrectiveAction : AuditableEntity<string>
{
    /// <summary>
    /// Links to the finding this action addresses (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string FindingId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the finding
    /// </summary>
    public virtual Finding Finding { get; set; } = null!;

    /// <summary>
    /// Description of the corrective or preventive action to be taken. Candidate for multi-language support.
    /// </summary>
    [Required]
    public string ActionDescription { get; set; } = string.Empty;

    /// <summary>
    /// User responsible for implementing this action (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string AssignedToUserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the assigned user
    /// </summary>
    public virtual User AssignedToUser { get; set; } = null!;

    /// <summary>
    /// Target date for completing this corrective action
    /// </summary>
    public DateOnly DueDate { get; set; }

    /// <summary>
    /// Actual date when this corrective action was completed
    /// </summary>
    public DateOnly? CompletionDate { get; set; }

    /// <summary>
    /// Notes regarding verification or evidence of completion for this action
    /// </summary>
    public string? EvidenceNotes { get; set; }

    /// <summary>
    /// Current status of the corrective action
    /// </summary>
    public CorrectiveActionStatus Status { get; set; } = CorrectiveActionStatus.Assigned;

    /// <summary>
    /// Indicates if the corrective action is overdue
    /// </summary>
    public bool IsOverdue => DueDate < DateOnly.FromDateTime(DateTime.UtcNow) && Status != CorrectiveActionStatus.VerifiedClosed && Status != CorrectiveActionStatus.Cancelled;

    /// <summary>
    /// Indicates if the corrective action is completed
    /// </summary>
    public bool IsCompleted => CompletionDate.HasValue;

    /// <summary>
    /// Indicates if the corrective action is in progress
    /// </summary>
    public bool IsInProgress => Status == CorrectiveActionStatus.InProgress;

    /// <summary>
    /// Indicates if the corrective action is closed
    /// </summary>
    public bool IsClosed => Status == CorrectiveActionStatus.VerifiedClosed;

    /// <summary>
    /// Gets the number of days until due date (negative if overdue)
    /// </summary>
    public int DaysUntilDue => (DueDate.ToDateTime(TimeOnly.MinValue) - DateTime.UtcNow).Days;
}
