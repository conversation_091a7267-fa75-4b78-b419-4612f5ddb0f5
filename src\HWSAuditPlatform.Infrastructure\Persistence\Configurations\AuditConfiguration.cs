using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Infrastructure.Persistence.Configurations;

/// <summary>
/// Entity Framework configuration for Audit entity
/// </summary>
public class AuditConfiguration : IEntityTypeConfiguration<Audit>
{
    public void Configure(EntityTypeBuilder<Audit> builder)
    {
        builder.ToTable("Audits");

        // Primary Key
        builder.HasKey(a => a.Id);
        builder.Property(a => a.Id)
            .HasMaxLength(25)
            .IsRequired();

        // Properties
        builder.Property(a => a.AssignmentType)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(a => a.AssignedToUserGroupId)
            .HasMaxLength(25)
            .IsRequired(false);

        builder.Property(a => a.AssignedToUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        builder.Property(a => a.ScheduledDate)
            .IsRequired();

        builder.Property(a => a.DueDate)
            .IsRequired(false);

        builder.Property(a => a.StartedAt)
            .IsRequired(false);

        builder.Property(a => a.CompletedAt)
            .IsRequired(false);

        builder.Property(a => a.OverallStatus)
            .HasConversion<string>()
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(a => a.OverallScore)
            .HasColumnType("decimal(7,2)")
            .IsRequired(false);

        builder.Property(a => a.ManagerComments)
            .HasColumnType("text")
            .IsRequired(false);

        builder.Property(a => a.ReviewedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        builder.Property(a => a.ReviewedAt)
            .IsRequired(false);

        builder.Property(a => a.RecurringAuditSettingId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Auditable properties
        builder.Property(a => a.CreatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(a => a.UpdatedAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(a => a.RecordVersion)
            .IsRequired()
            .HasDefaultValue(1)
            .IsConcurrencyToken();

        builder.Property(a => a.CreatedByUserId)
            .HasMaxLength(25)
            .IsRequired();

        builder.Property(a => a.UpdatedByUserId)
            .HasMaxLength(25)
            .IsRequired(false);

        // Indexes
        builder.HasIndex(a => a.AuditTemplateId)
            .HasDatabaseName("IX_Audits_AuditTemplateId");

        builder.HasIndex(a => a.AssignedToUserId)
            .HasDatabaseName("IX_Audits_AssignedToUserId");

        builder.HasIndex(a => a.AssignedToUserGroupId)
            .HasDatabaseName("IX_Audits_AssignedToUserGroupId");

        builder.HasIndex(a => a.OverallStatus)
            .HasDatabaseName("IX_Audits_OverallStatus");

        builder.HasIndex(a => a.ScheduledDate)
            .HasDatabaseName("IX_Audits_ScheduledDate");

        builder.HasIndex(a => a.DueDate)
            .HasDatabaseName("IX_Audits_DueDate");

        builder.HasIndex(a => a.FactoryId)
            .HasDatabaseName("IX_Audits_FactoryId");

        builder.HasIndex(a => a.AreaId)
            .HasDatabaseName("IX_Audits_AreaId");

        // Relationships
        builder.HasOne(a => a.AuditTemplate)
            .WithMany()
            .HasForeignKey(a => a.AuditTemplateId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.AssignedToUserGroup)
            .WithMany()
            .HasForeignKey(a => a.AssignedToUserGroupId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        builder.HasOne(a => a.AssignedToUser)
            .WithMany()
            .HasForeignKey(a => a.AssignedToUserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        builder.HasOne(a => a.Factory)
            .WithMany()
            .HasForeignKey(a => a.FactoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.Area)
            .WithMany()
            .HasForeignKey(a => a.AreaId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(a => a.SubArea)
            .WithMany()
            .HasForeignKey(a => a.SubAreaId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        builder.HasOne(a => a.ReviewedByUser)
            .WithMany()
            .HasForeignKey(a => a.ReviewedByUserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        builder.HasOne(a => a.RecurringAuditSetting)
            .WithMany()
            .HasForeignKey(a => a.RecurringAuditSettingId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        // Ignore domain events
        builder.Ignore(a => a.DomainEvents);
    }
}
