using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Domain.Entities.Audits;

/// <summary>
/// Represents an attachment (evidence) linked to an audit answer.
/// Maps to the AuditAttachments table in the database.
/// </summary>
public class AuditAttachment : AuditableEntity<string>
{
    /// <summary>
    /// Links to the audit answer this attachment provides evidence for (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string AuditAnswerId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the audit answer
    /// </summary>
    public virtual AuditAnswer AuditAnswer { get; set; } = null!;

    /// <summary>
    /// Original name of the uploaded file
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// Relative path or identifier for the file in blob storage/file system
    /// </summary>
    [Required]
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// MIME type of the file (e.g., image/jpeg, application/pdf)
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// Size of the file in bytes
    /// </summary>
    public long? FileSize { get; set; }

    /// <summary>
    /// User who uploaded this attachment (CUID FK)
    /// </summary>
    [Required]
    [MaxLength(25)]
    public string UploadedByUserId { get; set; } = string.Empty;

    /// <summary>
    /// Navigation property for the user who uploaded the attachment
    /// </summary>
    public virtual User UploadedByUser { get; set; } = null!;

    /// <summary>
    /// Timestamp when the user uploaded this attachment (client-side)
    /// </summary>
    public DateTime UploadedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Optional description of the attachment
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Indicates if this is an image file
    /// </summary>
    public bool IsImage => ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Indicates if this is a document file
    /// </summary>
    public bool IsDocument => ContentType.StartsWith("application/", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Gets the file extension from the filename
    /// </summary>
    public string FileExtension => Path.GetExtension(FileName);

    /// <summary>
    /// Gets a human-readable file size
    /// </summary>
    public string GetFormattedFileSize()
    {
        if (!FileSize.HasValue)
            return "Unknown";

        var size = FileSize.Value;
        string[] sizes = { "B", "KB", "MB", "GB" };
        int order = 0;
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size = size / 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }
}
