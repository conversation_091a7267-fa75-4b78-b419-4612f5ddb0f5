using System.ComponentModel.DataAnnotations;
using HWSAuditPlatform.Domain.Common;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Domain.Entities.Templates;

/// <summary>
/// Represents an audit template that defines the structure and content of audits.
/// Maps to the AuditTemplates table in the database.
/// </summary>
public class AuditTemplate : AuditableEntity<int>, IAggregateRoot
{
    /// <summary>
    /// Name of the audit template
    /// </summary>
    [Required]
    [MaxLength(255)]
    public string TemplateName { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of the template's purpose and scope
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Design version of the template, not for concurrency control.
    /// Incremented for significant changes to questions/structure.
    /// </summary>
    public int Version { get; set; } = 1;

    /// <summary>
    /// Indicates if this template version is finalized and ready for use in new audits
    /// </summary>
    public bool IsPublished { get; set; } = false;

    /// <summary>
    /// Indicates if this template version can be used (for soft deletion or retiring old versions)
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Navigation property for question groups in this template
    /// </summary>
    public virtual ICollection<QuestionGroup> QuestionGroups { get; set; } = new List<QuestionGroup>();

    /// <summary>
    /// Navigation property for questions in this template
    /// </summary>
    public virtual ICollection<Question> Questions { get; set; } = new List<Question>();

    /// <summary>
    /// Gets the full template identifier including version
    /// </summary>
    public string FullName => $"{TemplateName} v{Version}";

    /// <summary>
    /// Indicates if this template can be used for new audits
    /// </summary>
    public bool CanBeUsed => IsPublished && IsActive;
}
